import database from '../models/database.js';
import { config } from '../config/index.js';
import logger from '../utils/logger.js';

class TraderEvaluator {
  constructor() {
    this.evaluationConfig = config.evaluation;
  }

  // 主要評估方法 - 評估所有交易員並生成排名
  async evaluateAllTraders(timeframe = '30d') {
    try {
      logger.info(`Starting trader evaluation for timeframe: ${timeframe}`);
      
      const timeframeDays = this.parseTimeframe(timeframe);
      const startTime = Date.now() - (timeframeDays * 24 * 60 * 60 * 1000);
      
      // 獲取符合條件的交易員
      const eligibleTraders = await this.getEligibleTraders(startTime);
      
      logger.info(`Found ${eligibleTraders.length} eligible traders for evaluation`);
      
      // 評估每個交易員
      const evaluatedTraders = [];
      for (const trader of eligibleTraders) {
        try {
          const evaluation = await this.evaluateTrader(trader.address, startTime);
          if (evaluation && evaluation.score > 0) {
            evaluatedTraders.push({
              ...trader,
              ...evaluation
            });
          }
        } catch (error) {
          logger.error(`Error evaluating trader ${trader.address}:`, error);
        }
      }
      
      // 排序並生成排名
      const rankedTraders = this.rankTraders(evaluatedTraders);
      
      // 保存排名結果
      await this.saveRankings(rankedTraders, timeframe);
      
      logger.info(`Evaluation completed. ${rankedTraders.length} traders ranked`);
      
      return rankedTraders;
    } catch (error) {
      logger.error('Error in evaluateAllTraders:', error);
      throw error;
    }
  }

  // 評估單個交易員
  async evaluateTrader(address, startTime = null) {
    try {
      if (!startTime) {
        startTime = Date.now() - (this.evaluationConfig.evaluationPeriodDays * 24 * 60 * 60 * 1000);
      }

      // 獲取交易數據
      const trades = await this.getTraderTrades(address, startTime);
      
      if (trades.length < this.evaluationConfig.minTradesForEvaluation) {
        return null;
      }

      // 計算各項指標
      const metrics = await this.calculateMetrics(trades);
      
      // 檢查是否符合最低要求
      if (!this.meetsMinimumRequirements(metrics)) {
        return null;
      }

      // 計算綜合評分
      const score = this.calculateCompositeScore(metrics);
      
      return {
        ...metrics,
        score,
        evaluationPeriod: {
          startTime,
          endTime: Date.now(),
          tradeCount: trades.length
        }
      };
    } catch (error) {
      logger.error(`Error evaluating trader ${address}:`, error);
      throw error;
    }
  }

  // 獲取符合條件的交易員
  async getEligibleTraders(startTime) {
    try {
      const query = `
        SELECT DISTINCT tr.address, tr.total_trades, tr.total_volume
        FROM traders tr
        JOIN trades t ON tr.address = t.trader_address
        WHERE t.timestamp >= ?
        GROUP BY tr.address
        HAVING COUNT(*) >= ?
      `;

      return await database.all(query, [
        startTime,
        this.evaluationConfig.minTradesForEvaluation
      ]);
    } catch (error) {
      logger.error('Error getting eligible traders:', error);
      throw error;
    }
  }

  // 獲取交易員的交易記錄
  async getTraderTrades(address, startTime) {
    try {
      const query = `
        SELECT * FROM trades 
        WHERE trader_address = ? AND timestamp >= ?
        ORDER BY timestamp ASC
      `;
      
      return await database.all(query, [address, startTime]);
    } catch (error) {
      logger.error(`Error getting trader trades for ${address}:`, error);
      throw error;
    }
  }

  // 計算交易員指標
  async calculateMetrics(trades) {
    try {
      const totalTrades = trades.length;
      const totalVolume = trades.reduce((sum, trade) => sum + (trade.size * trade.price), 0);
      const totalPnl = trades.reduce((sum, trade) => sum + trade.pnl, 0);
      
      // 勝率計算
      const winningTrades = trades.filter(trade => trade.pnl > 0);
      const winRate = winningTrades.length / totalTrades;
      
      // 盈利能力計算
      const profitability = totalPnl / totalVolume;
      
      // 平均交易規模
      const avgTradeSize = totalVolume / totalTrades;
      
      // 計算收益序列
      const returns = this.calculateReturns(trades);
      
      // 夏普比率
      const sharpeRatio = this.calculateSharpeRatio(returns);
      
      // 最大回撤
      const maxDrawdown = this.calculateMaxDrawdown(returns);
      
      // 一致性指標 (收益的標準差)
      const consistency = this.calculateConsistency(returns);
      
      // 風險調整收益
      const riskAdjustedReturn = sharpeRatio;
      
      // 交易頻率 (每天平均交易次數)
      const tradingPeriodDays = (trades[trades.length - 1].timestamp - trades[0].timestamp) / (24 * 60 * 60 * 1000);
      const tradingFrequency = totalTrades / Math.max(tradingPeriodDays, 1);
      
      return {
        totalTrades,
        totalVolume,
        totalPnl,
        winRate,
        profitability,
        sharpeRatio,
        maxDrawdown,
        consistency,
        avgTradeSize,
        tradingFrequency,
        riskAdjustedReturn
      };
    } catch (error) {
      logger.error('Error calculating metrics:', error);
      throw error;
    }
  }

  // 計算收益序列
  calculateReturns(trades) {
    const returns = [];
    let cumulativePnl = 0;
    
    for (const trade of trades) {
      const returnPct = trade.pnl / (trade.size * trade.price);
      returns.push(returnPct);
      cumulativePnl += trade.pnl;
    }
    
    return returns;
  }

  // 計算夏普比率
  calculateSharpeRatio(returns) {
    if (returns.length < 2) return 0;
    
    const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / (returns.length - 1);
    const stdDev = Math.sqrt(variance);
    
    // 假設無風險利率為 0
    return stdDev === 0 ? 0 : mean / stdDev;
  }

  // 計算最大回撤
  calculateMaxDrawdown(returns) {
    let maxDrawdown = 0;
    let peak = 0;
    let cumulative = 0;
    
    for (const ret of returns) {
      cumulative += ret;
      if (cumulative > peak) {
        peak = cumulative;
      }
      const drawdown = peak === 0 ? 0 : (peak - cumulative) / Math.abs(peak);
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown;
      }
    }
    
    return maxDrawdown;
  }

  // 計算一致性 (收益穩定性)
  calculateConsistency(returns) {
    if (returns.length < 2) return 0;
    
    const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / returns.length;
    const stdDev = Math.sqrt(variance);
    
    // 一致性分數：平均收益與標準差的比率
    return stdDev === 0 ? 1 : Math.abs(mean) / stdDev;
  }

  // 檢查是否符合最低要求
  meetsMinimumRequirements(metrics) {
    const thresholds = this.evaluationConfig.thresholds;
    
    return (
      metrics.winRate >= thresholds.minWinRate &&
      metrics.profitability >= thresholds.minProfitability &&
      metrics.maxDrawdown <= thresholds.maxDrawdown &&
      metrics.sharpeRatio >= thresholds.minSharpeRatio &&
      metrics.totalVolume >= thresholds.minTradingVolume
    );
  }

  // 計算綜合評分
  calculateCompositeScore(metrics) {
    const weights = this.evaluationConfig.weights;
    
    // 標準化各項指標 (0-100 分)
    const normalizedMetrics = {
      winRate: Math.min(metrics.winRate * 100, 100),
      profitability: Math.min(Math.max(metrics.profitability * 1000, 0), 100),
      sharpeRatio: Math.min(Math.max(metrics.sharpeRatio * 20, 0), 100),
      maxDrawdown: Math.max(100 - (metrics.maxDrawdown * 100), 0),
      consistency: Math.min(metrics.consistency * 10, 100)
    };
    
    // 計算加權平均分
    const score = (
      normalizedMetrics.winRate * weights.winRate +
      normalizedMetrics.profitability * weights.profitability +
      normalizedMetrics.sharpeRatio * weights.sharpeRatio +
      normalizedMetrics.maxDrawdown * weights.maxDrawdown +
      normalizedMetrics.consistency * weights.consistency
    );
    
    return Math.round(score * 100) / 100; // 保留兩位小數
  }

  // 對交易員進行排名
  rankTraders(traders) {
    return traders
      .sort((a, b) => b.score - a.score)
      .slice(0, this.evaluationConfig.topTradersCount)
      .map((trader, index) => ({
        ...trader,
        rank: index + 1
      }));
  }

  // 保存排名結果
  async saveRankings(rankedTraders, timeframe) {
    try {
      const timestamp = Date.now();
      
      await database.transaction(async () => {
        // 清除舊的排名數據
        await database.run(
          'DELETE FROM trader_rankings WHERE timeframe = ? AND timestamp < ?',
          [timeframe, timestamp - (24 * 60 * 60 * 1000)] // 保留24小時內的數據
        );
        
        // 插入新的排名數據
        for (const trader of rankedTraders) {
          await database.run(
            `INSERT INTO trader_rankings (
              trader_address, rank_position, score, timeframe, timestamp
            ) VALUES (?, ?, ?, ?, ?)`,
            [trader.address, trader.rank, trader.score, timeframe, timestamp]
          );
        }
      });
      
      logger.info(`Saved rankings for ${rankedTraders.length} traders`);
    } catch (error) {
      logger.error('Error saving rankings:', error);
      throw error;
    }
  }

  // 獲取排名結果
  async getRankings(timeframe = '30d', limit = 100) {
    try {
      const query = `
        SELECT 
          tr.trader_address,
          tr.rank_position,
          tr.score,
          tr.timestamp,
          t.total_trades,
          t.total_volume,
          t.total_pnl,
          t.win_rate
        FROM trader_rankings tr
        JOIN traders t ON tr.trader_address = t.address
        WHERE tr.timeframe = ?
        ORDER BY tr.rank_position ASC
        LIMIT ?
      `;
      
      return await database.all(query, [timeframe, limit]);
    } catch (error) {
      logger.error('Error getting rankings:', error);
      throw error;
    }
  }

  // 解析時間框架
  parseTimeframe(timeframe) {
    const timeframeMap = {
      '1h': 1/24,
      '4h': 4/24,
      '24h': 1,
      '7d': 7,
      '30d': 30
    };
    
    return timeframeMap[timeframe] || 30;
  }

  // 獲取交易員詳細評估信息
  async getTraderEvaluation(address, timeframe = '30d') {
    try {
      const timeframeDays = this.parseTimeframe(timeframe);
      const startTime = Date.now() - (timeframeDays * 24 * 60 * 60 * 1000);
      
      return await this.evaluateTrader(address, startTime);
    } catch (error) {
      logger.error(`Error getting trader evaluation for ${address}:`, error);
      throw error;
    }
  }

  // 獲取頂尖交易員地址列表
  async getTopTraderAddresses(limit = 100) {
    try {
      // 為了效率，我們直接查詢最新的排名結果
      const query = `
        SELECT trader_address
        FROM trader_rankings
        ORDER BY timestamp DESC, rank_position ASC
        LIMIT ?
      `;
      
      const results = await database.all(query, [limit]);
      return results.map(r => r.trader_address);
    } catch (error) {
      logger.error('Error getting top trader addresses:', error);
      throw error;
    }
  }
}

export default TraderEvaluator;
