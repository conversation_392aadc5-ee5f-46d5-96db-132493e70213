import TelegramBot from 'node-telegram-bot-api';
import dotenv from 'dotenv';

dotenv.config();

async function testFixedData() {
  try {
    console.log('🧪 測試修復後的數據...');
    
    const bot = new TelegramBot(process.env.TELEGRAM_BOT_TOKEN, { polling: false });
    const chatId = 251366744; // <PERSON>'s chat ID
    
    console.log('📤 發送 /top 命令 (測試修復後的數據)...');
    await bot.sendMessage(chatId, '/top');
    
    console.log('⏳ 等待 5 秒...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    console.log('📤 發送 /top 5 命令...');
    await bot.sendMessage(chatId, '/top 5');
    
    console.log('⏳ 等待 4 秒...');
    await new Promise(resolve => setTimeout(resolve, 4000));
    
    console.log('📤 發送 /stats 命令 (測試修復後的統計)...');
    await bot.sendMessage(chatId, '/stats');
    
    console.log('⏳ 等待 4 秒...');
    await new Promise(resolve => setTimeout(resolve, 4000));
    
    console.log('📤 發送 /top 3 1h 命令 (測試參數解析)...');
    await bot.sendMessage(chatId, '/top 3 1h');
    
    console.log('✅ 所有測試命令已發送！');
    console.log('🔍 請檢查 Telegram 中是否顯示真實的 PnL 和勝率數據');
    console.log('💰 應該能看到具體的美元金額和百分比勝率');
    
  } catch (error) {
    console.error('❌ 測試失敗:', error.message);
  }
}

testFixedData();
