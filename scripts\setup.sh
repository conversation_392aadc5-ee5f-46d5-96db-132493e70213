#!/bin/bash

# Hyperliquid Trader Tracker 初始化腳本

set -e

echo "🔧 初始化 Hyperliquid Trader Tracker 開發環境..."

# 檢查 Node.js 版本
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安裝，請先安裝 Node.js 18+"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js 版本過低，需要 18+，當前版本: $(node -v)"
    exit 1
fi

echo "✅ Node.js 版本: $(node -v)"

# 檢查 npm
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安裝"
    exit 1
fi

echo "✅ npm 版本: $(npm -v)"

# 安裝後端依賴
echo "📦 安裝後端依賴..."
npm install

# 安裝前端依賴
echo "📦 安裝前端依賴..."
cd client
npm install
cd ..

# 創建必要的目錄
echo "📁 創建必要的目錄..."
mkdir -p logs
mkdir -p data

# 複製環境配置文件
if [ ! -f .env ]; then
    echo "📋 複製環境配置文件..."
    cp .env.example .env
    echo "⚠️  請編輯 .env 文件配置您的環境變量"
fi

# 初始化數據庫
echo "🗄️ 初始化數據庫..."
node -e "
const database = require('./src/models/database.js').default;
database.initialize().then(() => {
    console.log('✅ 數據庫初始化完成');
    process.exit(0);
}).catch(err => {
    console.error('❌ 數據庫初始化失敗:', err);
    process.exit(1);
});
"

# 構建前端
echo "🔨 構建前端..."
cd client
npm run build
cd ..

echo "🎉 初始化完成！"
echo ""
echo "🚀 啟動命令:"
echo "  開發模式: npm run dev"
echo "  生產模式: npm start"
echo "  前端開發: cd client && npm run dev"
echo ""
echo "📊 其他命令:"
echo "  運行測試: npm test"
echo "  檢查代碼: npm run lint"
echo "  格式化代碼: npm run format"
