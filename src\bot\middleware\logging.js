import logger from '../../utils/logger.js';
import database from '../../models/database.js';

// 日誌記錄中間件
async function loggingMiddleware(msg, bot) {
  try {
    const startTime = Date.now();
    const user = msg.from;
    const chatId = msg.chat.id;
    const text = msg.text || '';

    // 記錄基本信息
    logger.info(`Message from user ${user.id} (${user.username || user.first_name}): ${text}`);

    // 如果是指令，記錄到數據庫
    if (text.startsWith('/')) {
      const [command, ...args] = text.slice(1).split(' ');
      
      try {
        // 獲取用戶記錄
        const userRecord = await bot.getUser(user.id);
        
        if (userRecord) {
          // 記錄指令使用
          await database.run(`
            INSERT INTO bot_usage_logs 
            (user_id, command, parameters, response_time, created_at)
            VALUES (?, ?, ?, ?, ?)
          `, [
            userRecord.id,
            command,
            JSON.stringify(args),
            0, // 響應時間稍後更新
            Date.now()
          ]);
        }
      } catch (dbError) {
        logger.error('Failed to log command usage:', dbError);
        // 不阻止請求繼續處理
      }
    }

    // 在消息對象上添加開始時間，用於計算響應時間
    msg._startTime = startTime;

    return true;
  } catch (error) {
    logger.error('Logging middleware error:', error);
    return true; // 出錯時允許通過
  }
}

export default loggingMiddleware;
