import TelegramBot from 'node-telegram-bot-api';
import dotenv from 'dotenv';

dotenv.config();

console.log('🚀 Starting minimal Telegram bot...');

const token = process.env.TELEGRAM_BOT_TOKEN;

if (!token) {
  console.error('❌ TELEGRAM_BOT_TOKEN is required');
  process.exit(1);
}

console.log('✅ Bot token found');

try {
  const bot = new TelegramBot(token, { polling: true });
  
  console.log('🤖 Bot created, setting up handlers...');
  
  bot.on('message', (msg) => {
    const chatId = msg.chat.id;
    const text = msg.text;
    
    console.log(`📨 Message from ${msg.from.first_name} (${msg.from.id}): ${text}`);
    
    if (text === '/start') {
      bot.sendMessage(chatId, '🎉 Hello! I am working!');
    } else if (text === '/test') {
      bot.sendMessage(chatId, '✅ Test successful!');
    } else {
      bot.sendMessage(chatId, `Echo: ${text}`);
    }
  });
  
  bot.on('error', (error) => {
    console.error('❌ Bot error:', error);
  });
  
  bot.on('polling_error', (error) => {
    console.error('❌ Polling error:', error);
  });
  
  console.log('✅ Minimal bot is running!');
  
  // 每30秒輸出狀態
  setInterval(() => {
    console.log(`🔄 Bot status: Running at ${new Date().toLocaleString()}`);
  }, 30000);
  
} catch (error) {
  console.error('❌ Failed to start bot:', error);
}
