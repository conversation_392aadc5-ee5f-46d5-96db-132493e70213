{"name": "hyperliquid-trader-tracker", "version": "1.0.0", "description": "實時篩選優秀 Hyperliquid 交易員並統計開單情況的系統", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "build": "npm run build:client", "build:client": "cd client && npm run build", "dev:client": "cd client && npm run dev", "test": "node --experimental-vm-modules node_modules/jest/bin/jest.js", "lint": "eslint src/", "format": "prettier --write src/", "setup": "chmod +x scripts/setup.sh && ./scripts/setup.sh", "deploy": "chmod +x scripts/deploy.sh && ./scripts/deploy.sh", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop ecosystem.config.js", "pm2:restart": "pm2 restart ecosystem.config.js", "pm2:logs": "pm2 logs hyperliquid-tracker"}, "keywords": ["hyperliquid", "trading", "analytics", "real-time", "cryptocurrency"], "author": "Your Name", "license": "MIT", "dependencies": {"axios": "^1.6.2", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "joi": "^17.11.0", "lodash": "^4.17.21", "moment": "^2.29.4", "node-cron": "^3.0.3", "node-telegram-bot-api": "^0.66.0", "redis": "^4.6.10", "socket.io": "^4.7.4", "sqlite3": "^5.1.6", "winston": "^3.11.0", "ws": "^8.14.2"}, "devDependencies": {"@types/node": "^20.10.4", "eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "pm2": "^5.3.0", "prettier": "^3.1.0"}, "engines": {"node": ">=18.0.0"}}