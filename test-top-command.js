import TelegramBot from 'node-telegram-bot-api';
import dotenv from 'dotenv';

dotenv.config();

async function testTopCommand() {
  try {
    console.log('🧪 測試 /top 命令功能...');
    
    const bot = new TelegramBot(process.env.TELEGRAM_BOT_TOKEN, { polling: false });
    const chatId = 251366744; // <PERSON>'s chat ID
    
    console.log('📤 發送 /top 命令...');
    await bot.sendMessage(chatId, '/top');
    
    console.log('⏳ 等待 5 秒...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    console.log('📤 發送 /top 5 命令 (前5名)...');
    await bot.sendMessage(chatId, '/top 5');
    
    console.log('⏳ 等待 5 秒...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    console.log('📤 發送 /top 1h 命令 (1小時時間範圍)...');
    await bot.sendMessage(chatId, '/top 1h');
    
    console.log('✅ 所有 /top 命令測試已發送！');
    console.log('🔍 請檢查 Telegram 中的響應和機器人日誌');
    
  } catch (error) {
    console.error('❌ 測試失敗:', error.message);
  }
}

testTopCommand();
