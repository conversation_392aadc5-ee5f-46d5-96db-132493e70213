2025-06-25 02:19:19 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:19 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:19 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:19 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:19 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:19 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:19 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:20 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:20 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:21 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:21 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:22 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:22 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:23 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:23 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:24 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:24 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:25 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:25 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:26 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:26 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:27 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:27 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:28 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:28 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:29 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:29 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:30 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:30 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:31 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:32 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:32 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:33 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:33 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:34 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:34 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:35 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:35 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:36 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:36 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:37 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:37 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:38 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:38 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:39 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:39 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:40 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:40 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:41 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:41 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:42 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:42 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:43 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:43 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:44 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:44 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:45 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:46 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:46 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:47 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:47 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:48 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:48 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:49 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:49 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:50 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:50 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:51 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:51 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:52 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:52 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:53 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:53 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:54 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:19:54 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:01 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:01 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:01 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:01 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:02 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:02 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:02 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:02 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:03 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:03 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:04 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:04 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:05 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:05 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:06 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:06 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:07 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:07 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:08 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:08 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:09 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:09 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:10 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:10 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:11 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:11 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:12 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:12 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:13 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:13 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:14 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:14 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:15 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:15 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:16 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:16 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:17 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:17 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:18 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:18 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:19 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:19 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:20 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:20 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:21 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:21 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:22 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:23 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:23 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:24 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:24 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:25 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:25 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:26 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:26 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:27 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:27 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:28 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:28 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:29 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:29 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:30 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:30 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:31 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:31 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:32 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:32 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:33 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:33 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:34 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:34 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:35 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:35 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:36 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:36 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:37 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:37 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:38 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:38 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:39 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:39 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:40 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:20:40 [ERROR]: Redis cache error: connect ECONNREFUSED ::1:6379
Error: connect ECONNREFUSED ::1:6379
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)
2025-06-25 02:21:24 [ERROR]: Database all error: SQLITE_ERROR: no such column: t.address
Error: SQLITE_ERROR: no such column: t.address
2025-06-25 02:21:24 [ERROR]: Error getting eligible traders: SQLITE_ERROR: no such column: t.address
Error: SQLITE_ERROR: no such column: t.address
2025-06-25 02:21:24 [ERROR]: Error in evaluateAllTraders: SQLITE_ERROR: no such column: t.address
Error: SQLITE_ERROR: no such column: t.address
2025-06-25 02:21:24 [ERROR]: Initial trader evaluation failed: SQLITE_ERROR: no such column: t.address
Error: SQLITE_ERROR: no such column: t.address
2025-06-25 02:22:08 [ERROR]: Uncaught Exception: listen EADDRINUSE: address already in use :::3000
Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1817:16)
    at listenInCluster (node:net:1865:12)
    at Server.listen (node:net:1953:7)
    at HyperliquidTraderTracker.start (file:///C:/Users/<USER>/Desktop/hyperliquid/src/index.js:240:19)
2025-06-25 02:22:08 [ERROR]: WebSocket error: WebSocket was closed before the connection was established
Error: WebSocket was closed before the connection was established
    at WebSocket.close (C:\Users\<USER>\Desktop\hyperliquid\node_modules\ws\lib\websocket.js:299:7)
    at HyperliquidAPI.disconnect (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:209:15)
    at DataCollector.stop (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:84:14)
    at shutdown (file:///C:/Users/<USER>/Desktop/hyperliquid/src/index.js:270:36)
    at process.<anonymous> (file:///C:/Users/<USER>/Desktop/hyperliquid/src/index.js:296:7)
    at process.emit (node:events:517:28)
    at process._fatalException (node:internal/process/execution:169:25)
2025-06-25 02:22:08 [ERROR]: Hyperliquid API error: WebSocket was closed before the connection was established
Error: WebSocket was closed before the connection was established
    at WebSocket.close (C:\Users\<USER>\Desktop\hyperliquid\node_modules\ws\lib\websocket.js:299:7)
    at HyperliquidAPI.disconnect (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:209:15)
    at DataCollector.stop (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:84:14)
    at shutdown (file:///C:/Users/<USER>/Desktop/hyperliquid/src/index.js:270:36)
    at process.<anonymous> (file:///C:/Users/<USER>/Desktop/hyperliquid/src/index.js:296:7)
    at process.emit (node:events:517:28)
    at process._fatalException (node:internal/process/execution:169:25)
2025-06-25 02:22:14 [ERROR]: API Response Error:
2025-06-25 02:22:14 [ERROR]: Error fetching fills for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:22:14 [ERROR]: Error updating trader data for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:23:14 [ERROR]: API Response Error:
2025-06-25 02:23:14 [ERROR]: Error fetching fills for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:25:14 [ERROR]: Error updating trader data for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:25:15 [ERROR]: API Response Error:
2025-06-25 02:25:15 [ERROR]: Error fetching fills for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:25:15 [ERROR]: Error updating trader data for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:26:14 [ERROR]: API Response Error:
2025-06-25 02:26:14 [ERROR]: Error fetching fills for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:26:14 [ERROR]: Error updating trader data for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:27:14 [ERROR]: API Response Error:
2025-06-25 02:27:14 [ERROR]: Error fetching fills for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:27:14 [ERROR]: Error updating trader data for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:28:14 [ERROR]: API Response Error:
2025-06-25 02:28:14 [ERROR]: Error fetching fills for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:28:14 [ERROR]: Error updating trader data for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:29:14 [ERROR]: API Response Error:
2025-06-25 02:29:14 [ERROR]: Error fetching fills for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:29:14 [ERROR]: Error updating trader data for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:30:14 [ERROR]: API Response Error:
2025-06-25 02:30:14 [ERROR]: Error fetching fills for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:30:14 [ERROR]: Error updating trader data for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:31:14 [ERROR]: API Response Error:
2025-06-25 02:31:14 [ERROR]: Error fetching fills for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:31:14 [ERROR]: Error updating trader data for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:32:14 [ERROR]: API Response Error:
2025-06-25 02:32:14 [ERROR]: Error fetching fills for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:32:14 [ERROR]: Error updating trader data for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:33:14 [ERROR]: API Response Error:
2025-06-25 02:33:14 [ERROR]: Error fetching fills for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:33:14 [ERROR]: Error updating trader data for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:34:14 [ERROR]: API Response Error:
2025-06-25 02:34:14 [ERROR]: Error fetching fills for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:34:14 [ERROR]: Error updating trader data for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:35:14 [ERROR]: API Response Error:
2025-06-25 02:35:14 [ERROR]: Error fetching fills for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:35:14 [ERROR]: Error updating trader data for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:36:14 [ERROR]: API Response Error:
2025-06-25 02:36:14 [ERROR]: Error fetching fills for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:36:14 [ERROR]: Error updating trader data for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:37:20 [ERROR]: Database all error: SQLITE_ERROR: no such column: t.address
Error: SQLITE_ERROR: no such column: t.address
2025-06-25 02:37:20 [ERROR]: Error getting eligible traders: SQLITE_ERROR: no such column: t.address
Error: SQLITE_ERROR: no such column: t.address
2025-06-25 02:37:20 [ERROR]: Error in evaluateAllTraders: SQLITE_ERROR: no such column: t.address
Error: SQLITE_ERROR: no such column: t.address
2025-06-25 02:37:20 [ERROR]: Initial trader evaluation failed: SQLITE_ERROR: no such column: t.address
Error: SQLITE_ERROR: no such column: t.address
2025-06-25 02:38:10 [ERROR]: API Response Error:
2025-06-25 02:38:10 [ERROR]: Error fetching fills for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:38:10 [ERROR]: Error updating trader data for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:39:10 [ERROR]: API Response Error:
2025-06-25 02:39:10 [ERROR]: Error fetching fills for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:39:10 [ERROR]: Error updating trader data for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:40:10 [ERROR]: API Response Error:
2025-06-25 02:40:10 [ERROR]: Error fetching fills for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:40:10 [ERROR]: Error updating trader data for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:41:10 [ERROR]: API Response Error:
2025-06-25 02:41:10 [ERROR]: Error fetching fills for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:41:10 [ERROR]: Error updating trader data for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:42:10 [ERROR]: API Response Error:
2025-06-25 02:42:10 [ERROR]: Error fetching fills for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:42:10 [ERROR]: Error updating trader data for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:43:10 [ERROR]: API Response Error:
2025-06-25 02:43:10 [ERROR]: Error fetching fills for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:43:10 [ERROR]: Error updating trader data for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:44:10 [ERROR]: API Response Error:
2025-06-25 02:44:10 [ERROR]: Error fetching fills for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:44:10 [ERROR]: Error updating trader data for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:45:10 [ERROR]: API Response Error:
2025-06-25 02:45:10 [ERROR]: Error fetching fills for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:45:10 [ERROR]: Error updating trader data for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:46:10 [ERROR]: API Response Error:
2025-06-25 02:46:10 [ERROR]: Error fetching fills for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:46:10 [ERROR]: Error updating trader data for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:47:10 [ERROR]: API Response Error:
2025-06-25 02:47:10 [ERROR]: Error fetching fills for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:47:10 [ERROR]: Error updating trader data for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:48:10 [ERROR]: API Response Error:
2025-06-25 02:48:10 [ERROR]: Error fetching fills for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:48:10 [ERROR]: Error updating trader data for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:49:10 [ERROR]: API Response Error:
2025-06-25 02:49:10 [ERROR]: Error fetching fills for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:49:10 [ERROR]: Error updating trader data for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:50:10 [ERROR]: API Response Error:
2025-06-25 02:50:10 [ERROR]: Error fetching fills for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:50:11 [ERROR]: Error updating trader data for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:51:10 [ERROR]: API Response Error:
2025-06-25 02:51:10 [ERROR]: Error fetching fills for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 02:51:10 [ERROR]: Error updating trader data for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 23:29:10 [ERROR]: Database all error: SQLITE_ERROR: no such column: t.address
Error: SQLITE_ERROR: no such column: t.address
2025-06-25 23:29:10 [ERROR]: Error getting eligible traders: SQLITE_ERROR: no such column: t.address
Error: SQLITE_ERROR: no such column: t.address
2025-06-25 23:29:10 [ERROR]: Error in evaluateAllTraders: SQLITE_ERROR: no such column: t.address
Error: SQLITE_ERROR: no such column: t.address
2025-06-25 23:29:10 [ERROR]: Initial trader evaluation failed: SQLITE_ERROR: no such column: t.address
Error: SQLITE_ERROR: no such column: t.address
2025-06-25 23:30:00 [ERROR]: API Response Error:
2025-06-25 23:30:00 [ERROR]: Error fetching fills for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 23:30:00 [ERROR]: Error updating trader data for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 23:31:00 [ERROR]: API Response Error:
2025-06-25 23:31:00 [ERROR]: Error fetching fills for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 23:31:00 [ERROR]: Error updating trader data for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 23:32:00 [ERROR]: API Response Error:
2025-06-25 23:32:00 [ERROR]: Error fetching fills for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 23:32:00 [ERROR]: Error updating trader data for undefined: Request failed with status code 422
AxiosError: Request failed with status code 422
    at settle (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:529:35)
    at endReadableNT (node:internal/streams/readable:1400:12)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (file:///C:/Users/<USER>/Desktop/hyperliquid/node_modules/axios/lib/core/Axios.js:45:41)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async HyperliquidAPI.getUserFills (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/hyperliquidApi.js:90:24)
    at async DataCollector.updateTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:351:21)
    at async DataCollector.collectTraderData (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:341:9)
    at async Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/hyperliquid/src/services/dataCollector.js:96:9)
2025-06-25 23:32:18 [ERROR]: Database run error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:32:18 [ERROR]: Error getting or creating trader: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:32:18 [ERROR]: Error processing trade: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:32:18 [ERROR]: Database run error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:32:18 [ERROR]: Error getting or creating trader: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:32:18 [ERROR]: Error processing trade: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:32:28 [ERROR]: Database all error: SQLITE_ERROR: no such column: t.address
Error: SQLITE_ERROR: no such column: t.address
2025-06-25 23:32:28 [ERROR]: Error getting eligible traders: SQLITE_ERROR: no such column: t.address
Error: SQLITE_ERROR: no such column: t.address
2025-06-25 23:32:28 [ERROR]: Error in evaluateAllTraders: SQLITE_ERROR: no such column: t.address
Error: SQLITE_ERROR: no such column: t.address
2025-06-25 23:32:28 [ERROR]: Initial trader evaluation failed: SQLITE_ERROR: no such column: t.address
Error: SQLITE_ERROR: no such column: t.address
2025-06-25 23:32:34 [ERROR]: Database run error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:32:34 [ERROR]: Error getting or creating trader: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:32:34 [ERROR]: Error processing trade: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:32:34 [ERROR]: Database run error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:32:34 [ERROR]: Error getting or creating trader: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:32:34 [ERROR]: Error processing trade: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:32:51 [ERROR]: Database run error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:32:51 [ERROR]: Error getting or creating trader: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:32:51 [ERROR]: Error processing trade: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:32:52 [ERROR]: Database run error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:32:52 [ERROR]: Error getting or creating trader: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:32:52 [ERROR]: Error processing trade: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:33:21 [ERROR]: Database all error: SQLITE_ERROR: no such column: t.address
Error: SQLITE_ERROR: no such column: t.address
2025-06-25 23:33:21 [ERROR]: Error getting eligible traders: SQLITE_ERROR: no such column: t.address
Error: SQLITE_ERROR: no such column: t.address
2025-06-25 23:33:21 [ERROR]: Error in evaluateAllTraders: SQLITE_ERROR: no such column: t.address
Error: SQLITE_ERROR: no such column: t.address
2025-06-25 23:33:21 [ERROR]: Initial trader evaluation failed: SQLITE_ERROR: no such column: t.address
Error: SQLITE_ERROR: no such column: t.address
2025-06-25 23:34:01 [ERROR]: Database run error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:34:01 [ERROR]: Error getting or creating trader: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:34:01 [ERROR]: Error processing trade: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:34:03 [ERROR]: Database run error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:34:03 [ERROR]: Error getting or creating trader: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:34:03 [ERROR]: Error processing trade: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:34:31 [ERROR]: Database run error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:34:31 [ERROR]: Error getting or creating trader: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:34:31 [ERROR]: Error processing trade: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:34:31 [ERROR]: Database run error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:34:31 [ERROR]: Error getting or creating trader: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:34:31 [ERROR]: Error processing trade: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:34:50 [ERROR]: Database run error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:34:50 [ERROR]: Error getting or creating trader: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-25 23:34:50 [ERROR]: Error processing trade: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: traders.address
2025-06-26 01:43:34 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:43:42 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:43:50 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:43:59 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:44:07 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:44:15 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:44:23 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:44:31 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:44:40 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:44:48 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:44:56 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:45:08 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:45:16 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:45:25 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:45:33 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:45:41 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:45:55 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:46:03 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:46:11 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:46:20 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:46:28 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:46:36 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:46:44 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:46:52 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:47:01 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:47:09 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:47:16 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:47:24 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:47:31 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:47:39 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:47:47 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:47:54 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:48:02 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:48:10 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:48:17 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
2025-06-26 01:48:24 [ERROR]: Telegram polling error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
Error: ETELEGRAM: 409 Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
    at C:\Users\<USER>\Desktop\hyperliquid\node_modules\node-telegram-bot-api\src\telegram.js:316:15
    at tryCatcher (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\util.js:16:23)
    at Promise._settlePromiseFromHandler (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:547:31)
    at Promise._settlePromise (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:604:18)
    at Promise._settlePromise0 (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:649:10)
    at Promise._settlePromises (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\promise.js:729:18)
    at _drainQueueStep (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:93:12)
    at _drainQueue (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:86:9)
    at Async._drainQueues (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:102:5)
    at Async.drainQueues [as _onImmediate] (C:\Users\<USER>\Desktop\hyperliquid\node_modules\bluebird\js\release\async.js:15:14)
    at process.processImmediate (node:internal/timers:476:21)
