#!/bin/bash

# Hyperliquid Trader Tracker 部署腳本

set -e

echo "🚀 開始部署 Hyperliquid Trader Tracker..."

# 檢查 Docker 是否安裝
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安裝，請先安裝 Docker"
    exit 1
fi

# 檢查 Docker Compose 是否安裝
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安裝，請先安裝 Docker Compose"
    exit 1
fi

# 創建必要的目錄
echo "📁 創建必要的目錄..."
mkdir -p logs
mkdir -p data
mkdir -p ssl

# 複製環境配置文件
if [ ! -f .env ]; then
    echo "📋 複製環境配置文件..."
    cp .env.example .env
    echo "⚠️  請編輯 .env 文件配置您的環境變量"
fi

# 構建並啟動服務
echo "🔨 構建 Docker 鏡像..."
docker-compose build

echo "🚀 啟動服務..."
docker-compose up -d

# 等待服務啟動
echo "⏳ 等待服務啟動..."
sleep 10

# 檢查服務狀態
echo "🔍 檢查服務狀態..."
docker-compose ps

# 檢查應用健康狀態
echo "🏥 檢查應用健康狀態..."
if curl -f http://localhost:3000/health > /dev/null 2>&1; then
    echo "✅ 應用啟動成功！"
    echo "🌐 訪問地址: http://localhost:3000"
else
    echo "❌ 應用啟動失敗，請檢查日誌"
    docker-compose logs app
    exit 1
fi

echo "🎉 部署完成！"
echo ""
echo "📊 監控命令:"
echo "  查看日誌: docker-compose logs -f app"
echo "  查看狀態: docker-compose ps"
echo "  停止服務: docker-compose down"
echo "  重啟服務: docker-compose restart"
