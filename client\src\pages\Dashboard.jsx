import React, { useState, useEffect } from 'react'
import { Card, Row, Col, Statistic, Ty<PERSON><PERSON>, <PERSON><PERSON>, Spin } from 'antd'
import { 
  TrophyOutlined, 
  RiseOutlined, 
  FallOutlined,
  DollarOutlined 
} from '@ant-design/icons'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts'
import { useSocket } from '../hooks/useSocket'
import { useApi } from '../hooks/useApi'

const { Title, Text } = Typography

const COLORS = ['#52c41a', '#ff4d4f', '#1890ff', '#faad14']

function Dashboard() {
  const [report, setReport] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  
  const { data: systemStats } = useApi('/api/system/stats')
  const socket = useSocket()

  useEffect(() => {
    fetchReport()
  }, [])

  useEffect(() => {
    if (socket) {
      socket.on('statsUpdate', (newReport) => {
        setReport(newReport)
      })

      socket.on('initialData', (data) => {
        if (data.type === 'report') {
          setReport(data.data)
        }
      })

      return () => {
        socket.off('statsUpdate')
        socket.off('initialData')
      }
    }
  }, [socket])

  const fetchReport = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/stats/report?timeframe=1h')
      const result = await response.json()
      
      if (result.success) {
        setReport(result.data)
      } else {
        setError(result.error)
      }
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="loading-container">
        <Spin size="large" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="error-container">
        <Alert
          message="載入失敗"
          description={error}
          type="error"
          showIcon
        />
      </div>
    )
  }

  const overallStats = report?.overall || {}
  const topCoins = report?.summary?.topCoins || []

  // 準備圖表數據
  const positionData = [
    { name: '做多', value: overallStats.long_percentage || 0, color: '#52c41a' },
    { name: '做空', value: overallStats.short_percentage || 0, color: '#ff4d4f' }
  ]

  const coinVolumeData = topCoins.map(coin => ({
    name: coin.coin,
    volume: coin.volume,
    longPercentage: coin.longPercentage
  }))

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>
          實時交易統計
          <span className="real-time-indicator" style={{ marginLeft: 16 }}>
            <span className="real-time-dot"></span>
            即時更新
          </span>
        </Title>
      </div>

      {/* 系統統計 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="總交易員數"
              value={systemStats?.totalTraders || 0}
              prefix={<TrophyOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="活躍交易員"
              value={overallStats.active_traders || 0}
              prefix={<RiseOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="總交易量"
              value={overallStats.total_volume || 0}
              prefix={<DollarOutlined />}
              precision={2}
              suffix="USD"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="總持倉數"
              value={(overallStats.long_positions || 0) + (overallStats.short_positions || 0)}
              prefix={<FallOutlined />}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={16}>
        {/* 持倉分佈 */}
        <Col span={12}>
          <Card title="優秀交易員持倉分佈" className="stats-card">
            <div style={{ height: 300 }}>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={positionData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, value }) => `${name}: ${value.toFixed(1)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {positionData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
            
            <div style={{ marginTop: 16, textAlign: 'center' }}>
              <div style={{ display: 'flex', justifyContent: 'space-around' }}>
                <div>
                  <Text strong className="positive">
                    做多: {(overallStats.long_percentage || 0).toFixed(1)}%
                  </Text>
                  <br />
                  <Text type="secondary">
                    {overallStats.long_positions || 0} 筆
                  </Text>
                </div>
                <div>
                  <Text strong className="negative">
                    做空: {(overallStats.short_percentage || 0).toFixed(1)}%
                  </Text>
                  <br />
                  <Text type="secondary">
                    {overallStats.short_positions || 0} 筆
                  </Text>
                </div>
              </div>
            </div>
          </Card>
        </Col>

        {/* 熱門幣種 */}
        <Col span={12}>
          <Card title="熱門交易幣種" className="stats-card">
            <div style={{ height: 300 }}>
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={coinVolumeData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip 
                    formatter={(value, name) => [
                      name === 'volume' ? `$${value.toLocaleString()}` : `${value.toFixed(1)}%`,
                      name === 'volume' ? '交易量' : '做多比例'
                    ]}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="volume" 
                    stroke="#1890ff" 
                    strokeWidth={2}
                    yAxisId="left"
                  />
                  <Line 
                    type="monotone" 
                    dataKey="longPercentage" 
                    stroke="#52c41a" 
                    strokeWidth={2}
                    yAxisId="right"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 重要提示 */}
      <Row style={{ marginTop: 24 }}>
        <Col span={24}>
          <Alert
            message="數據說明"
            description={
              <div>
                <p>• 統計範圍：過去 1 小時內的優秀交易員開單情況</p>
                <p>• 優秀交易員標準：基於 30 天內的勝率、收益率、夏普比率等綜合評分</p>
                <p>• 數據每分鐘自動更新，確保信息的即時性</p>
                <p>• 當前共追蹤 {overallStats.total_traders || 0} 名優秀交易員，其中 {overallStats.active_traders || 0} 名在過去 1 小時內有交易活動</p>
              </div>
            }
            type="info"
            showIcon
          />
        </Col>
      </Row>
    </div>
  )
}

export default Dashboard
