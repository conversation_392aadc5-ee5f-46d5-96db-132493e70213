import dotenv from 'dotenv';
import TelegramBot from 'node-telegram-bot-api';
import database from './src/models/database.js';

dotenv.config();

async function testBotCore() {
  console.log('🤖 Testing Telegram Bot Core...');
  
  try {
    // 檢查是否設置了 Bot Token
    if (!process.env.TELEGRAM_BOT_TOKEN) {
      console.warn('❌ TELEGRAM_BOT_TOKEN not set. Please set it in .env file to test the bot.');
      console.log('📝 Example: TELEGRAM_BOT_TOKEN=1234567890:ABCdefGHIjklMNOpqrsTUVwxyz');
      return;
    }

    console.log('✅ Bot token found');

    // 初始化數據庫
    try {
      await database.initialize();
      console.log('✅ Database initialized');
    } catch (error) {
      console.error('❌ Database initialization failed:', error.message);
      return;
    }

    // 創建 Bot 實例
    const bot = new TelegramBot(process.env.TELEGRAM_BOT_TOKEN, { 
      polling: true,
      request: {
        agentOptions: {
          keepAlive: true,
          family: 4
        }
      }
    });

    console.log('🎉 Telegram Bot created successfully!');

    // 測試 Bot 信息
    try {
      const me = await bot.getMe();
      console.log('🤖 Bot info:', {
        id: me.id,
        username: me.username,
        first_name: me.first_name
      });
    } catch (error) {
      console.error('❌ Failed to get bot info:', error.message);
      await database.close();
      return;
    }

    // 初始化 Bot 數據庫表
    try {
      await initializeBotTables();
      console.log('✅ Bot database tables initialized');
    } catch (error) {
      console.error('❌ Failed to initialize bot tables:', error.message);
    }

    // 設置基本指令處理
    bot.onText(/\/start/, async (msg) => {
      const chatId = msg.chat.id;
      const userId = msg.from.id;
      const username = msg.from.username || msg.from.first_name;
      
      console.log('📨 Received /start from:', username);
      
      try {
        // 記錄用戶
        await recordUser(userId, username, msg.from);
        
        await bot.sendMessage(chatId, 
          '🎉 歡迎使用 Hyperliquid 交易員追蹤機器人！\n\n' +
          '🚀 這是一個專業的交易員分析工具，可以幫助您:\n' +
          '• 📊 追蹤優秀交易員的表現\n' +
          '• 📈 分析市場趨勢和統計數據\n' +
          '• 🔔 接收重要交易提醒\n\n' +
          '使用 /help 查看所有可用指令。'
        );
      } catch (error) {
        console.error('Error handling /start:', error.message);
        await bot.sendMessage(chatId, '❌ 處理指令時發生錯誤，請稍後再試。');
      }
    });

    bot.onText(/\/help/, async (msg) => {
      const chatId = msg.chat.id;
      console.log('📨 Received /help from:', msg.from.username || msg.from.first_name);
      
      await bot.sendMessage(chatId, 
        '📚 Hyperliquid 交易員追蹤機器人指令\n\n' +
        '🔧 基本指令:\n' +
        '• /start - 開始使用機器人\n' +
        '• /help - 顯示此幫助信息\n' +
        '• /status - 查看系統狀態\n\n' +
        '📊 查詢指令:\n' +
        '• /top - 查看頂級交易員\n' +
        '• /stats - 查看交易統計\n' +
        '• /trader <地址> - 查看特定交易員\n\n' +
        '🔔 通知指令:\n' +
        '• /alerts - 管理通知設置\n' +
        '• /subscribe - 訂閱通知\n' +
        '• /unsubscribe - 取消訂閱\n\n' +
        '💡 更多功能正在開發中...'
      );
    });

    bot.onText(/\/status/, async (msg) => {
      const chatId = msg.chat.id;
      console.log('📨 Received /status from:', msg.from.username || msg.from.first_name);
      
      try {
        const stats = await getSystemStats();
        await bot.sendMessage(chatId, 
          '📊 系統狀態報告\n\n' +
          `🤖 機器人: 運行中\n` +
          `💾 數據庫: 已連接\n` +
          `👥 註冊用戶: ${stats.users}\n` +
          `📈 交易員數量: ${stats.traders}\n` +
          `📊 交易記錄: ${stats.trades}\n` +
          `🕐 最後更新: ${new Date().toLocaleString()}`
        );
      } catch (error) {
        console.error('Error getting status:', error.message);
        await bot.sendMessage(chatId, '❌ 獲取系統狀態時發生錯誤。');
      }
    });

    // 處理所有其他消息
    bot.on('message', async (msg) => {
      if (!msg.text || msg.text.startsWith('/')) return;
      
      const chatId = msg.chat.id;
      console.log('📨 Received message from:', msg.from.username || msg.from.first_name, ':', msg.text);
      
      await bot.sendMessage(chatId, 
        '🤖 我收到了您的消息！\n\n' +
        `您說: "${msg.text}"\n\n` +
        '請使用 /help 查看可用指令，或直接使用指令與我互動。'
      );
    });

    console.log('🚀 Bot is now running and ready to receive messages.');
    console.log('💬 Send /start to your bot to test it.');
    console.log('🛑 Press Ctrl+C to stop the bot');

    // 優雅關閉
    process.on('SIGINT', async () => {
      console.log('\n🛑 Shutting down bot...');
      try {
        bot.stopPolling();
        await database.close();
        console.log('✅ Bot stopped successfully');
      } catch (error) {
        console.error('❌ Error during shutdown:', error.message);
      }
      process.exit(0);
    });

    // 錯誤處理
    bot.on('error', (error) => {
      console.error('❌ Bot error:', error.message);
    });

    bot.on('polling_error', (error) => {
      console.error('❌ Polling error:', error.message);
    });

  } catch (error) {
    console.error('❌ Bot test failed:', error.message);
    console.error('Stack:', error.stack);
    await database.close();
    process.exit(1);
  }
}

// 輔助函數
async function initializeBotTables() {
  const db = database.getDatabase();
  
  // 創建 bot_users 表
  await db.exec(`
    CREATE TABLE IF NOT EXISTS bot_users (
      id INTEGER PRIMARY KEY,
      telegram_id INTEGER UNIQUE NOT NULL,
      username TEXT,
      first_name TEXT,
      last_name TEXT,
      language_code TEXT,
      is_bot INTEGER DEFAULT 0,
      is_banned INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // 創建 bot_usage_logs 表
  await db.exec(`
    CREATE TABLE IF NOT EXISTS bot_usage_logs (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      command TEXT NOT NULL,
      parameters TEXT,
      response_time INTEGER,
      success INTEGER DEFAULT 1,
      error_message TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES bot_users (telegram_id)
    )
  `);
}

async function recordUser(telegramId, username, userInfo) {
  const db = database.getDatabase();
  
  await db.run(`
    INSERT OR REPLACE INTO bot_users 
    (telegram_id, username, first_name, last_name, language_code, is_bot, updated_at)
    VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
  `, [
    telegramId,
    username,
    userInfo.first_name,
    userInfo.last_name,
    userInfo.language_code,
    userInfo.is_bot ? 1 : 0
  ]);
}

async function getSystemStats() {
  const db = database.getDatabase();
  
  const users = await db.get('SELECT COUNT(*) as count FROM bot_users');
  const traders = await db.get('SELECT COUNT(*) as count FROM traders');
  const trades = await db.get('SELECT COUNT(*) as count FROM trades');
  
  return {
    users: users.count,
    traders: traders.count,
    trades: trades.count
  };
}

console.log('🚀 Starting bot core test...');
testBotCore().catch(error => {
  console.error('❌ Unhandled error:', error.message);
  console.error('Stack:', error.stack);
  process.exit(1);
});
