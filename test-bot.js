import dotenv from 'dotenv';
import HyperliquidBot from './src/bot/index.js';
import database from './src/models/database.js';
import logger from './src/utils/logger.js';

dotenv.config();

async function testBot() {
  try {
    logger.info('Testing Telegram Bot...');

    // 檢查是否設置了 Bot Token
    if (!process.env.TELEGRAM_BOT_TOKEN) {
      logger.warn('TELEGRAM_BOT_TOKEN not set. Please set it in .env file to test the bot.');
      logger.info('Example: TELEGRAM_BOT_TOKEN=1234567890:ABCdefGHIjklMNOpqrsTUVwxyz');
      return;
    }

    // 初始化數據庫
    await database.initialize();
    logger.info('Database initialized');

    // 創建並初始化 Bot
    const bot = new HyperliquidBot();
    await bot.initialize();
    
    logger.info('Telegram Bot initialized successfully!');
    logger.info('<PERSON><PERSON> is now running and ready to receive messages.');
    logger.info('Send /start to your bot to test it.');
    
    // 保持運行
    process.on('SIGINT', async () => {
      logger.info('Shutting down bot...');
      await bot.stop();
      await database.close();
      process.exit(0);
    });

  } catch (error) {
    logger.error('Bot test failed:', error);
    process.exit(1);
  }
}

testBot();
