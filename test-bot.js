import dotenv from 'dotenv';
import HyperliquidBot from './src/bot/index.js';
import database from './src/models/database.js';
import logger from './src/utils/logger.js';

dotenv.config();

async function testBot() {
  try {
    console.log('🤖 Testing Telegram Bot...');

    // 檢查是否設置了 Bot Token
    if (!process.env.TELEGRAM_BOT_TOKEN) {
      console.warn('❌ TELEGRAM_BOT_TOKEN not set. Please set it in .env file to test the bot.');
      console.log('📝 Example: TELEGRAM_BOT_TOKEN=1234567890:ABCdefGHIjklMNOpqrsTUVwxyz');
      return;
    }

    console.log('✅ Bot token found');

    // 初始化數據庫
    try {
      await database.initialize();
      console.log('✅ Database initialized');
    } catch (error) {
      console.error('❌ Database initialization failed:', error.message);
      return;
    }

    // 創建並初始化 Bot
    try {
      const bot = new HyperliquidBot();
      await bot.initialize();

      console.log('🎉 Telegram Bot initialized successfully!');
      console.log('🚀 Bot is now running and ready to receive messages.');
      console.log('💬 Send /start to your bot to test it.');
      console.log('🛑 Press Ctrl+C to stop the bot');

      // 保持運行
      process.on('SIGINT', async () => {
        console.log('\n🛑 Shutting down bot...');
        try {
          await bot.stop();
          await database.close();
          console.log('✅ Bot stopped successfully');
        } catch (error) {
          console.error('❌ Error during shutdown:', error.message);
        }
        process.exit(0);
      });

    } catch (error) {
      console.error('❌ Bot initialization failed:', error.message);
      await database.close();
      process.exit(1);
    }

  } catch (error) {
    console.error('❌ Bot test failed:', error.message);
    process.exit(1);
  }
}

testBot();
