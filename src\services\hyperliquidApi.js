import axios from 'axios';
import WebSocket from 'ws';
import EventEmitter from 'events';
import { config } from '../config/index.js';
import logger from '../utils/logger.js';

class HyperliquidAPI extends EventEmitter {
  constructor() {
    super();
    this.apiUrl = config.hyperliquid.useTestnet 
      ? config.hyperliquid.testnetApiUrl 
      : config.hyperliquid.apiUrl;
    this.wsUrl = config.hyperliquid.useTestnet 
      ? config.hyperliquid.testnetWsUrl 
      : config.hyperliquid.wsUrl;
    
    this.ws = null;
    this.isConnected = false;
    this.subscriptions = new Set();
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 5000;

    this.setupAxios();
  }

  setupAxios() {
    this.httpClient = axios.create({
      baseURL: this.apiUrl,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // 請求攔截器
    this.httpClient.interceptors.request.use(
      (config) => {
        logger.debug(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        logger.error('API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // 響應攔截器
    this.httpClient.interceptors.response.use(
      (response) => {
        logger.debug(`API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        logger.error('API Response Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  // REST API 方法

  async getAllMids() {
    try {
      const response = await this.httpClient.post('/info', {
        type: 'allMids'
      });
      return response.data;
    } catch (error) {
      logger.error('Error fetching all mids:', error);
      throw error;
    }
  }

  async getUserOpenOrders(userAddress) {
    try {
      const response = await this.httpClient.post('/info', {
        type: 'openOrders',
        user: userAddress
      });
      return response.data;
    } catch (error) {
      logger.error(`Error fetching open orders for ${userAddress}:`, error);
      throw error;
    }
  }

  async getUserFills(userAddress, aggregateByTime = false) {
    try {
      const response = await this.httpClient.post('/info', {
        type: 'userFills',
        user: userAddress,
        aggregateByTime
      });
      return response.data;
    } catch (error) {
      logger.error(`Error fetching fills for ${userAddress}:`, error);
      throw error;
    }
  }

  async getUserFillsByTime(userAddress, startTime, endTime, aggregateByTime = false) {
    try {
      const response = await this.httpClient.post('/info', {
        type: 'userFillsByTime',
        user: userAddress,
        startTime,
        endTime,
        aggregateByTime
      });
      return response.data;
    } catch (error) {
      logger.error(`Error fetching fills by time for ${userAddress}:`, error);
      throw error;
    }
  }

  async getUserPortfolio(userAddress) {
    try {
      const response = await this.httpClient.post('/info', {
        type: 'portfolio',
        user: userAddress
      });
      return response.data;
    } catch (error) {
      logger.error(`Error fetching portfolio for ${userAddress}:`, error);
      throw error;
    }
  }

  async getL2Book(coin, nSigFigs = null, mantissa = null) {
    try {
      const payload = { type: 'l2Book', coin };
      if (nSigFigs !== null) payload.nSigFigs = nSigFigs;
      if (mantissa !== null) payload.mantissa = mantissa;

      const response = await this.httpClient.post('/info', payload);
      return response.data;
    } catch (error) {
      logger.error(`Error fetching L2 book for ${coin}:`, error);
      throw error;
    }
  }

  async getCandleSnapshot(coin, interval, startTime, endTime) {
    try {
      const response = await this.httpClient.post('/info', {
        type: 'candleSnapshot',
        req: {
          coin,
          interval,
          startTime,
          endTime
        }
      });
      return response.data;
    } catch (error) {
      logger.error(`Error fetching candle snapshot for ${coin}:`, error);
      throw error;
    }
  }

  // WebSocket 方法

  connect() {
    if (this.isConnected) {
      logger.warn('WebSocket already connected');
      return;
    }

    logger.info(`Connecting to Hyperliquid WebSocket: ${this.wsUrl}`);
    
    this.ws = new WebSocket(this.wsUrl);

    this.ws.on('open', () => {
      logger.info('WebSocket connected successfully');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.emit('connected');
      
      // 重新訂閱之前的訂閱
      this.resubscribe();
    });

    this.ws.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString());
        this.handleMessage(message);
      } catch (error) {
        logger.error('Error parsing WebSocket message:', error);
      }
    });

    this.ws.on('close', () => {
      logger.warn('WebSocket connection closed');
      this.isConnected = false;
      this.emit('disconnected');
      this.handleReconnect();
    });

    this.ws.on('error', (error) => {
      logger.error('WebSocket error:', error);
      this.emit('error', error);
    });
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
      this.isConnected = false;
    }
  }

  subscribe(subscription) {
    if (!this.isConnected) {
      logger.warn('WebSocket not connected, queuing subscription');
      this.subscriptions.add(subscription);
      return;
    }

    const message = {
      method: 'subscribe',
      subscription
    };

    logger.debug('Subscribing to:', subscription);
    this.ws.send(JSON.stringify(message));
    this.subscriptions.add(subscription);
  }

  unsubscribe(subscription) {
    const message = {
      method: 'unsubscribe',
      subscription
    };

    if (this.isConnected) {
      this.ws.send(JSON.stringify(message));
    }
    
    this.subscriptions.delete(subscription);
  }

  // 訂閱交易數據
  subscribeTrades(coin) {
    this.subscribe({ type: 'trades', coin });
  }

  // 訂閱訂單簿數據
  subscribeL2Book(coin) {
    this.subscribe({ type: 'l2Book', coin });
  }

  // 訂閱用戶數據
  subscribeUserEvents(user) {
    this.subscribe({ type: 'userEvents', user });
  }

  // 私有方法

  handleMessage(message) {
    if (message.channel === 'subscriptionResponse') {
      logger.debug('Subscription response:', message.data);
      return;
    }

    // 發出對應的事件
    this.emit('message', message);
    
    if (message.channel === 'trades') {
      this.emit('trades', message.data);
    } else if (message.channel === 'l2Book') {
      this.emit('l2Book', message.data);
    } else if (message.channel === 'userEvents') {
      this.emit('userEvents', message.data);
    }
  }

  resubscribe() {
    for (const subscription of this.subscriptions) {
      this.subscribe(subscription);
    }
  }

  handleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      logger.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    logger.info(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
    
    setTimeout(() => {
      this.connect();
    }, this.reconnectDelay * this.reconnectAttempts);
  }
}

export default HyperliquidAPI;
