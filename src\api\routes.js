import express from 'express';
import TraderEvaluator from '../services/traderEvaluator.js';
import RealTimeStats from '../services/realTimeStats.js';
import cacheService from '../services/cacheService.js';
import database from '../models/database.js';
import logger from '../utils/logger.js';

const router = express.Router();
const traderEvaluator = new TraderEvaluator();

// 中間件：錯誤處理
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// 中間件：緩存
const cacheMiddleware = (keyGenerator, ttl = 300) => {
  return asyncHandler(async (req, res, next) => {
    const cacheKey = keyGenerator(req);
    const cached = await cacheService.get(cacheKey);
    
    if (cached) {
      return res.json(cached);
    }
    
    // 保存原始的 json 方法
    const originalJson = res.json;
    res.json = function(data) {
      // 緩存響應數據
      cacheService.set(cacheKey, data, ttl);
      return originalJson.call(this, data);
    };
    
    next();
  });
};

// 獲取優秀交易員排名
router.get('/traders/top', 
  cacheMiddleware(req => cacheService.keys.traderRankings(req.query.timeframe || '30d'), 300),
  asyncHandler(async (req, res) => {
    const { timeframe = '30d', limit = 100 } = req.query;
    
    const rankings = await traderEvaluator.getRankings(timeframe, parseInt(limit));
    
    res.json({
      success: true,
      data: {
        timeframe,
        rankings,
        total: rankings.length,
        timestamp: Date.now()
      }
    });
  })
);

// 獲取交易員詳細信息
router.get('/traders/:address',
  cacheMiddleware(req => cacheService.keys.traderEvaluation(req.params.address, req.query.timeframe || '30d'), 600),
  asyncHandler(async (req, res) => {
    const { address } = req.params;
    const { timeframe = '30d' } = req.query;
    
    // 獲取交易員基本信息
    const trader = await database.get('SELECT * FROM traders WHERE address = ?', [address]);
    
    if (!trader) {
      return res.status(404).json({
        success: false,
        error: 'Trader not found'
      });
    }
    
    // 獲取評估信息
    const evaluation = await traderEvaluator.getTraderEvaluation(address, timeframe);
    
    // 獲取最近的交易記錄
    const recentTrades = await database.all(
      'SELECT * FROM trades WHERE trader_address = ? ORDER BY timestamp DESC LIMIT 50',
      [address]
    );
    
    res.json({
      success: true,
      data: {
        trader,
        evaluation,
        recentTrades,
        timestamp: Date.now()
      }
    });
  })
);

// 獲取實時開單統計
router.get('/stats/positions',
  cacheMiddleware(req => cacheService.keys.realTimeStats(req.query.timeframe || '1h', req.query.coin), 60),
  asyncHandler(async (req, res) => {
    const { timeframe = '1h', coin } = req.query;
    
    let stats;
    if (coin) {
      stats = await req.app.locals.realTimeStats.getStats(timeframe, coin);
    } else {
      stats = await req.app.locals.realTimeStats.getStats(timeframe);
    }
    
    res.json({
      success: true,
      data: {
        timeframe,
        coin: coin || 'all',
        stats,
        timestamp: Date.now()
      }
    });
  })
);

// 獲取所有幣種的統計數據
router.get('/stats/coins',
  cacheMiddleware(req => cacheService.keys.realTimeStats(req.query.timeframe || '1h', 'all_coins'), 60),
  asyncHandler(async (req, res) => {
    const { timeframe = '1h' } = req.query;
    
    const coinStats = await req.app.locals.realTimeStats.getAllCoinStats(timeframe);
    
    res.json({
      success: true,
      data: {
        timeframe,
        coins: coinStats,
        timestamp: Date.now()
      }
    });
  })
);

// 獲取統計報告
router.get('/stats/report',
  cacheMiddleware(req => `report:${req.query.timeframe || '1h'}`, 120),
  asyncHandler(async (req, res) => {
    const { timeframe = '1h' } = req.query;
    
    const report = await req.app.locals.realTimeStats.generateReport(timeframe);
    
    res.json({
      success: true,
      data: report
    });
  })
);

// 獲取歷史統計數據
router.get('/stats/history',
  asyncHandler(async (req, res) => {
    const { timeframe = '1h', coin, hours = 24 } = req.query;
    
    const history = await req.app.locals.realTimeStats.getHistoricalStats(
      timeframe, 
      coin, 
      parseInt(hours)
    );
    
    res.json({
      success: true,
      data: {
        timeframe,
        coin: coin || 'all',
        hours: parseInt(hours),
        history,
        timestamp: Date.now()
      }
    });
  })
);

// 搜索交易員
router.get('/traders/search',
  asyncHandler(async (req, res) => {
    const { q, limit = 20 } = req.query;
    
    if (!q || q.length < 3) {
      return res.status(400).json({
        success: false,
        error: 'Search query must be at least 3 characters'
      });
    }
    
    const traders = await database.all(
      `SELECT address, total_trades, total_volume, total_pnl, win_rate, performance_score
       FROM traders 
       WHERE address LIKE ? 
       ORDER BY performance_score DESC 
       LIMIT ?`,
      [`%${q}%`, parseInt(limit)]
    );
    
    res.json({
      success: true,
      data: {
        query: q,
        traders,
        total: traders.length
      }
    });
  })
);

// 獲取交易員交易歷史
router.get('/traders/:address/trades',
  asyncHandler(async (req, res) => {
    const { address } = req.params;
    const { limit = 100, offset = 0, coin } = req.query;
    
    let query = 'SELECT * FROM trades WHERE trader_address = ?';
    const params = [address];
    
    if (coin) {
      query += ' AND coin = ?';
      params.push(coin);
    }
    
    query += ' ORDER BY timestamp DESC LIMIT ? OFFSET ?';
    params.push(parseInt(limit), parseInt(offset));
    
    const trades = await database.all(query, params);
    
    res.json({
      success: true,
      data: {
        address,
        trades,
        limit: parseInt(limit),
        offset: parseInt(offset)
      }
    });
  })
);

// 獲取系統統計
router.get('/system/stats',
  cacheMiddleware(() => 'system:stats', 300),
  asyncHandler(async (req, res) => {
    const totalTraders = await database.get('SELECT COUNT(*) as count FROM traders');
    const totalTrades = await database.get('SELECT COUNT(*) as count FROM trades');
    const activeTraders = await database.get(
      'SELECT COUNT(DISTINCT trader_address) as count FROM trades WHERE timestamp >= ?',
      [Date.now() - (24 * 60 * 60 * 1000)]
    );
    
    const cacheStats = await cacheService.getStats();
    
    res.json({
      success: true,
      data: {
        totalTraders: totalTraders.count,
        totalTrades: totalTrades.count,
        activeTraders: activeTraders.count,
        cache: cacheStats,
        uptime: process.uptime(),
        timestamp: Date.now()
      }
    });
  })
);

// 觸發重新評估
router.post('/admin/evaluate',
  asyncHandler(async (req, res) => {
    const { timeframe = '30d' } = req.body;
    
    logger.info(`Manual evaluation triggered for timeframe: ${timeframe}`);
    
    // 異步執行評估
    traderEvaluator.evaluateAllTraders(timeframe)
      .then(() => {
        logger.info('Manual evaluation completed');
      })
      .catch(error => {
        logger.error('Manual evaluation failed:', error);
      });
    
    res.json({
      success: true,
      message: 'Evaluation started',
      timeframe
    });
  })
);

// 清除緩存
router.post('/admin/cache/clear',
  asyncHandler(async (req, res) => {
    await cacheService.flush();
    
    res.json({
      success: true,
      message: 'Cache cleared'
    });
  })
);

// 錯誤處理中間件
router.use((error, req, res, next) => {
  logger.error('API Error:', error);
  
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: error.message
  });
});

export default router;
