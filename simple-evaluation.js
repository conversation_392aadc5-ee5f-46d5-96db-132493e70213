import database from './src/models/database.js';

async function simpleEvaluation() {
  try {
    console.log('🚀 Running simple trader evaluation...');
    
    await database.initialize();
    
    // 獲取最近 24h 有交易的交易員
    const activeTraders = await database.all(`
      SELECT 
        trader_address,
        COUNT(*) as trade_count,
        SUM(size * price) as total_volume,
        AVG(size * price) as avg_trade_size
      FROM trades 
      WHERE timestamp > ?
      GROUP BY trader_address 
      HAVING trade_count >= 5
      ORDER BY trade_count DESC 
      LIMIT 20
    `, [Date.now() - 24 * 60 * 60 * 1000]);
    
    console.log(`👥 Found ${activeTraders.length} active traders`);
    
    if (activeTraders.length === 0) {
      console.log('❌ No active traders found');
      process.exit(1);
    }
    
    // 為每個交易員計算簡單評分
    const rankings = [];
    
    for (const trader of activeTraders) {
      // 獲取交易詳情
      const trades = await database.all(
        'SELECT * FROM trades WHERE trader_address = ? AND timestamp > ? ORDER BY timestamp',
        [trader.trader_address, Date.now() - 24 * 60 * 60 * 1000]
      );
      
      // 計算基本指標
      const buyTrades = trades.filter(t => t.side === 'buy' || t.side === 'B');
      const sellTrades = trades.filter(t => t.side === 'sell' || t.side === 'S');
      
      // 簡單評分：交易數量 + 交易量
      const score = trader.trade_count * 0.1 + (trader.total_volume / 1000) * 0.01;
      
      rankings.push({
        trader_address: trader.trader_address,
        rank_position: 0, // 稍後設置
        score: score,
        timeframe: '24h',
        timestamp: Date.now(),
        trade_count: trader.trade_count,
        total_volume: trader.total_volume,
        buy_trades: buyTrades.length,
        sell_trades: sellTrades.length
      });
    }
    
    // 排序並設置排名
    rankings.sort((a, b) => b.score - a.score);
    rankings.forEach((ranking, index) => {
      ranking.rank_position = index + 1;
    });
    
    // 清除舊的 24h 排名
    await database.run('DELETE FROM trader_rankings WHERE timeframe = ?', ['24h']);
    
    // 插入新排名
    for (const ranking of rankings) {
      await database.run(`
        INSERT INTO trader_rankings 
        (trader_address, rank_position, score, timeframe, timestamp, created_at)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [
        ranking.trader_address,
        ranking.rank_position,
        ranking.score,
        ranking.timeframe,
        ranking.timestamp,
        Date.now()
      ]);
    }
    
    console.log(`✅ Generated ${rankings.length} rankings for 24h`);
    
    // 顯示前 10 名
    console.log('\n🏆 Top 10 Traders (24h):');
    rankings.slice(0, 10).forEach(ranking => {
      console.log(`  ${ranking.rank_position}. ${ranking.trader_address.slice(0, 8)}... - Score: ${ranking.score.toFixed(2)}, Trades: ${ranking.trade_count}, Volume: $${ranking.total_volume.toFixed(2)}`);
    });
    
    // 測試 Bot 查詢
    console.log('\n🤖 Testing bot query...');
    const botRankings = await database.all(`
      SELECT 
        tr.trader_address,
        tr.rank_position,
        tr.score,
        tr.timestamp,
        t.total_trades,
        t.total_volume,
        t.total_pnl,
        t.win_rate,
        t.performance_score
      FROM trader_rankings tr
      JOIN traders t ON tr.trader_address = t.address
      WHERE tr.timeframe = ?
      ORDER BY tr.rank_position ASC
      LIMIT 5
    `, ['24h']);
    
    console.log(`📊 Bot query returned ${botRankings.length} results`);
    if (botRankings.length > 0) {
      console.log('✅ Bot should now be able to show rankings!');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Simple evaluation failed:', error.message);
    console.error('Stack:', error.stack);
    process.exit(1);
  }
}

simpleEvaluation();
