import TelegramBot from 'node-telegram-bot-api';
import dotenv from 'dotenv';
import logger from './src/utils/logger.js';
import database from './src/models/database.js';

dotenv.config();

const token = process.env.TELEGRAM_BOT_TOKEN;

if (!token) {
  console.error('TELEGRAM_BOT_TOKEN is required');
  process.exit(1);
}

const bot = new TelegramBot(token, { polling: true });

// 簡單的消息處理
bot.on('message', async (msg) => {
  const chatId = msg.chat.id;
  const text = msg.text;
  
  logger.info(`Message from user ${msg.from.id} (${msg.from.first_name}): ${text}`);
  
  if (text === '/start') {
    await bot.sendMessage(chatId, `🎉 歡迎使用 Hyperliquid 交易員追蹤機器人！

🚀 這個機器人可以幫助您：
• 📊 查看頂級交易員排行榜
• 📈 獲取實時交易統計
• 🔍 搜索和分析特定交易員

💡 使用 /help 查看所有可用指令
🌟 使用 /top 查看當前頂級交易員`);
  }
  else if (text === '/help') {
    await bot.sendMessage(chatId, `📚 可用指令列表：

🔰 基本指令：
/start - 開始使用
/help - 顯示此幫助
/status - 系統狀態

👥 交易員查詢：
/top [數量] - 頂級交易員排行榜
/trader <地址> - 查詢特定交易員
/search <關鍵詞> - 搜索交易員

📊 統計查詢：
/stats [時間] - 交易統計

💡 提示：點擊指令可直接使用`);
  }
  else if (text === '/status') {
    await bot.sendMessage(chatId, `✅ 系統狀態正常

🔗 數據庫: 已連接
📊 數據收集: 運行中
🤖 機器人: 正常運行

⏰ 當前時間: ${new Date().toLocaleString('zh-TW')}`);
  }
  else if (text.startsWith('/top')) {
    try {
      await database.initialize();
      
      // 獲取交易員排行榜
      const rankings = await database.all(`
        SELECT address, total_trades, win_rate, total_pnl, total_volume
        FROM trader_rankings 
        ORDER BY total_pnl DESC 
        LIMIT 10
      `);
      
      if (rankings.length === 0) {
        await bot.sendMessage(chatId, '❌ 暫無交易員排行榜數據');
        return;
      }
      
      let message = '🏆 頂級交易員排行榜 (Top 10)\n\n';
      
      rankings.forEach((trader, index) => {
        const rank = index + 1;
        const emoji = rank === 1 ? '🥇' : rank === 2 ? '🥈' : rank === 3 ? '🥉' : '📊';
        const address = trader.address.slice(0, 6) + '...' + trader.address.slice(-4);
        const winRate = (trader.win_rate * 100).toFixed(1);
        const pnl = trader.total_pnl ? trader.total_pnl.toFixed(2) : '0.00';
        const volume = trader.total_volume ? (trader.total_volume / 1000).toFixed(1) + 'K' : '0';
        
        message += `${emoji} #${rank} ${address}\n`;
        message += `   💰 PnL: $${pnl}\n`;
        message += `   📈 勝率: ${winRate}%\n`;
        message += `   📊 交易量: $${volume}\n`;
        message += `   🔢 交易次數: ${trader.total_trades}\n\n`;
      });
      
      await bot.sendMessage(chatId, message);
      
    } catch (error) {
      logger.error('Error in /top command:', error);
      await bot.sendMessage(chatId, '❌ 獲取排行榜數據時發生錯誤');
    }
  }
  else if (text.startsWith('/stats')) {
    try {
      await database.initialize();
      
      // 獲取基本統計
      const traderCount = await database.get('SELECT COUNT(*) as count FROM traders');
      const tradeCount = await database.get('SELECT COUNT(*) as count FROM trades');
      const rankingCount = await database.get('SELECT COUNT(*) as count FROM trader_rankings');
      
      const message = `📊 系統統計數據

👥 總交易員數量: ${traderCount.count}
📈 總交易記錄: ${tradeCount.count}
🏆 排行榜交易員: ${rankingCount.count}

⏰ 更新時間: ${new Date().toLocaleString('zh-TW')}`;
      
      await bot.sendMessage(chatId, message);
      
    } catch (error) {
      logger.error('Error in /stats command:', error);
      await bot.sendMessage(chatId, '❌ 獲取統計數據時發生錯誤');
    }
  }
  else {
    await bot.sendMessage(chatId, '請使用指令與我互動。輸入 /help 查看可用指令。');
  }
});

// 錯誤處理
bot.on('error', (error) => {
  logger.error('Telegram bot error:', error);
});

bot.on('polling_error', (error) => {
  logger.error('Telegram polling error:', error);
});

console.log('Working Telegram bot started...');
logger.info('Working Telegram bot started successfully');

// 添加更多調試信息
bot.on('polling_error', (error) => {
  console.error('Polling error:', error);
  logger.error('Telegram polling error:', error);
});

bot.on('error', (error) => {
  console.error('Bot error:', error);
  logger.error('Telegram bot error:', error);
});

// 測試機器人是否能接收消息
setInterval(() => {
  console.log(`🔄 Bot is running... ${new Date().toLocaleString()}`);
}, 30000); // 每30秒輸出一次狀態
