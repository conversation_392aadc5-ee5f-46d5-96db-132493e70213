import TelegramBot from 'node-telegram-bot-api';
import dotenv from 'dotenv';

dotenv.config();

async function sendTestMessage() {
  console.log('📤 Sending test message to bot...');
  
  try {
    const token = process.env.TELEGRAM_BOT_TOKEN;
    
    if (!token) {
      console.error('❌ TELEGRAM_BOT_TOKEN not found');
      return;
    }
    
    const bot = new TelegramBot(token, { polling: false });
    
    // 獲取機器人信息
    const me = await bot.getMe();
    console.log(`🤖 Bot: @${me.username}`);
    
    // 獲取最近的更新來找到聊天 ID
    const updates = await bot.getUpdates({ limit: 10 });
    
    if (updates.length === 0) {
      console.log('❌ No recent messages found. Please send a message to the bot first.');
      return;
    }
    
    // 找到最近的聊天
    const lastUpdate = updates[updates.length - 1];
    const chatId = lastUpdate.message ? lastUpdate.message.chat.id : lastUpdate.callback_query?.message?.chat?.id;
    
    if (!chatId) {
      console.log('❌ Could not find chat ID');
      return;
    }
    
    console.log(`💬 Found chat ID: ${chatId}`);
    
    // 發送測試消息
    const testMessage = `🧪 測試消息 - ${new Date().toLocaleString('zh-TW')}

這是一個測試消息，用來檢查機器人是否正常運行。

請回復 /start 來測試機器人功能。`;
    
    const result = await bot.sendMessage(chatId, testMessage);
    console.log('✅ Test message sent successfully!');
    console.log(`📨 Message ID: ${result.message_id}`);
    
  } catch (error) {
    console.error('❌ Failed to send test message:', error.message);
  }
}

sendTestMessage();
