# 多階段構建 Dockerfile
FROM node:18-alpine AS frontend-builder

# 設置工作目錄
WORKDIR /app/client

# 複製前端 package.json 和 package-lock.json
COPY client/package*.json ./

# 安裝前端依賴
RUN npm ci --only=production

# 複製前端源代碼
COPY client/ ./

# 構建前端
RUN npm run build

# 後端構建階段
FROM node:18-alpine AS backend

# 安裝系統依賴
RUN apk add --no-cache \
    sqlite \
    python3 \
    make \
    g++

# 設置工作目錄
WORKDIR /app

# 複製後端 package.json 和 package-lock.json
COPY package*.json ./

# 安裝後端依賴
RUN npm ci --only=production

# 複製後端源代碼
COPY src/ ./src/
COPY .env.example ./.env

# 從前端構建階段複製構建結果
COPY --from=frontend-builder /app/client/dist ./client/dist

# 創建數據目錄
RUN mkdir -p /app/data

# 設置環境變量
ENV NODE_ENV=production
ENV PORT=3000

# 暴露端口
EXPOSE 3000

# 創建非 root 用戶
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# 更改文件所有權
RUN chown -R nodejs:nodejs /app
USER nodejs

# 健康檢查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# 啟動應用
CMD ["node", "src/index.js"]
