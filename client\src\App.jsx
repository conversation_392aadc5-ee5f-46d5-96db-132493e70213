import React from 'react'
import { Routes, Route } from 'react-router-dom'
import { Layout, Menu, Typography } from 'antd'
import { 
  DashboardOutlined, 
  TrophyOutlined, 
  BarChartOutlined,
  UserOutlined 
} from '@ant-design/icons'
import Dashboard from './pages/Dashboard'
import TopTraders from './pages/TopTraders'
import Statistics from './pages/Statistics'
import TraderDetail from './pages/TraderDetail'

const { Header, Content, Sider } = Layout
const { Title } = Typography

function App() {
  const [collapsed, setCollapsed] = React.useState(false)

  const menuItems = [
    {
      key: '/',
      icon: <DashboardOutlined />,
      label: '儀表板'
    },
    {
      key: '/traders',
      icon: <TrophyOutlined />,
      label: '優秀交易員'
    },
    {
      key: '/stats',
      icon: <BarChartOutlined />,
      label: '開單統計'
    }
  ]

  return (
    <Layout className="app-container">
      <Sider 
        collapsible 
        collapsed={collapsed} 
        onCollapse={setCollapsed}
        theme="dark"
      >
        <div style={{ 
          height: 32, 
          margin: 16, 
          background: 'rgba(255, 255, 255, 0.3)',
          borderRadius: 6,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontWeight: 'bold'
        }}>
          {collapsed ? 'HL' : 'Hyperliquid'}
        </div>
        <Menu
          theme="dark"
          defaultSelectedKeys={['/']}
          mode="inline"
          items={menuItems}
          onClick={({ key }) => {
            window.location.pathname = key
          }}
        />
      </Sider>
      
      <Layout>
        <Header style={{ 
          padding: '0 24px', 
          background: '#fff',
          display: 'flex',
          alignItems: 'center',
          borderBottom: '1px solid #f0f0f0'
        }}>
          <Title level={3} style={{ margin: 0 }}>
            Hyperliquid 交易員追蹤系統
          </Title>
        </Header>
        
        <Content className="main-content">
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/traders" element={<TopTraders />} />
            <Route path="/traders/:address" element={<TraderDetail />} />
            <Route path="/stats" element={<Statistics />} />
          </Routes>
        </Content>
      </Layout>
    </Layout>
  )
}

export default App
