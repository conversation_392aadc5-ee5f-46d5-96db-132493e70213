console.log('🚀 Starting basic test...');

try {
  console.log('📦 Testing imports...');
  
  // 測試 dotenv
  console.log('1. Testing dotenv...');
  import('dotenv').then(dotenv => {
    console.log('✅ dotenv imported successfully');
    dotenv.config();
    console.log('✅ dotenv configured');
    
    // 測試環境變量
    console.log('2. Testing environment variables...');
    if (process.env.TELEGRAM_BOT_TOKEN) {
      console.log('✅ TELEGRAM_BOT_TOKEN found');
      console.log('Token length:', process.env.TELEGRAM_BOT_TOKEN.length);
    } else {
      console.log('❌ TELEGRAM_BOT_TOKEN not found');
    }
    
    // 測試 Telegram Bot API
    console.log('3. Testing Telegram Bot API import...');
    import('node-telegram-bot-api').then(TelegramBotModule => {
      console.log('✅ node-telegram-bot-api imported successfully');
      
      const TelegramBot = TelegramBotModule.default;
      console.log('✅ TelegramBot constructor available');
      
      // 創建 Bot 實例
      console.log('4. Creating bot instance...');
      try {
        const bot = new TelegramBot(process.env.TELEGRAM_BOT_TOKEN, { 
          polling: false 
        });
        console.log('✅ Bot instance created successfully');
        
        // 測試 getMe
        console.log('5. Testing getMe...');
        bot.getMe().then(me => {
          console.log('✅ getMe successful:', {
            id: me.id,
            username: me.username,
            first_name: me.first_name
          });
          console.log('🎉 All tests passed!');
        }).catch(error => {
          console.error('❌ getMe failed:', error.message);
        });
        
      } catch (error) {
        console.error('❌ Bot creation failed:', error.message);
      }
      
    }).catch(error => {
      console.error('❌ Failed to import node-telegram-bot-api:', error.message);
    });
    
  }).catch(error => {
    console.error('❌ Failed to import dotenv:', error.message);
  });
  
} catch (error) {
  console.error('❌ Basic test failed:', error.message);
  console.error('Stack:', error.stack);
}
