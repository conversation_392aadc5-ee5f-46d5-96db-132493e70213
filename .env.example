# 服務器配置
PORT=3000
NODE_ENV=development

# Hyperliquid API 配置
HYPERLIQUID_API_URL=https://api.hyperliquid.xyz
HYPERLIQUID_WS_URL=wss://api.hyperliquid.xyz/ws
HYPERLIQUID_TESTNET_API_URL=https://api.hyperliquid-testnet.xyz
HYPERLIQUID_TESTNET_WS_URL=wss://api.hyperliquid-testnet.xyz/ws

# 數據庫配置
DATABASE_PATH=./data/traders.db

# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 日誌配置
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# 交易員評估配置
MIN_TRADES_FOR_EVALUATION=10
EVALUATION_PERIOD_DAYS=30
TOP_TRADERS_COUNT=100

# 統計配置
STATS_UPDATE_INTERVAL_MS=60000
POSITION_TRACKING_INTERVAL_MS=30000

# 安全配置
CORS_ORIGIN=http://localhost:3000
API_RATE_LIMIT=100

# 緩存配置
CACHE_TTL_SECONDS=300
TRADER_DATA_CACHE_TTL=3600
