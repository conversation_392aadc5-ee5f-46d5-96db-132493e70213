# 服務器配置
PORT=3000
NODE_ENV=development

# Hyperliquid API 配置
HYPERLIQUID_API_URL=https://api.hyperliquid.xyz
HYPERLIQUID_WS_URL=wss://api.hyperliquid.xyz/ws
HYPERLIQUID_TESTNET_API_URL=https://api.hyperliquid-testnet.xyz
HYPERLIQUID_TESTNET_WS_URL=wss://api.hyperliquid-testnet.xyz/ws

# 數據庫配置
DATABASE_PATH=./data/traders.db

# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 日誌配置
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# 交易員評估配置
MIN_TRADES_FOR_EVALUATION=10
EVALUATION_PERIOD_DAYS=30
TOP_TRADERS_COUNT=100

# 統計配置
STATS_UPDATE_INTERVAL_MS=60000
POSITION_TRACKING_INTERVAL_MS=30000

# 安全配置
CORS_ORIGIN=http://localhost:3000
API_RATE_LIMIT=100

# 緩存配置
CACHE_TTL_SECONDS=300
TRADER_DATA_CACHE_TTL=3600

# Telegram Bot 配置
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_USE_WEBHOOK=false
TELEGRAM_WEBHOOK_URL=https://your-domain.com/webhook
TELEGRAM_WEBHOOK_PORT=8443

# Bot 功能配置
BOT_ENABLE_ALERTS=true
BOT_ENABLE_EXPORT=true
BOT_ENABLE_ADVANCED_STATS=true
BOT_MAX_TOP_TRADERS=50
BOT_MAX_SEARCH_RESULTS=20

# Bot 速率限制
BOT_RATE_LIMIT_MAX=30
BOT_RATE_LIMIT_WINDOW=60000

# Bot 緩存配置
BOT_USER_CACHE_TTL=3600
BOT_STATS_CACHE_TTL=300
BOT_TRADER_CACHE_TTL=600

# Bot 分頁配置
BOT_DEFAULT_PAGE_SIZE=10
BOT_MAX_PAGE_SIZE=50

# Bot 語言配置
BOT_DEFAULT_LANGUAGE=zh

# Bot 管理員配置
BOT_ADMIN_USER_IDS=
BOT_ENABLE_ADMIN_COMMANDS=false

# Bot 通知配置
BOT_ENABLE_BROADCAST=false
BOT_MAX_BROADCAST_USERS=1000
BOT_BROADCAST_DELAY=100
