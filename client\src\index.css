@import 'antd/dist/reset.css';

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding: 24px;
}

.stats-card {
  margin-bottom: 16px;
}

.trader-card {
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.trader-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.rank-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  font-weight: bold;
  color: white;
  margin-right: 12px;
}

.rank-1 { background: linear-gradient(45deg, #ffd700, #ffed4e); }
.rank-2 { background: linear-gradient(45deg, #c0c0c0, #e8e8e8); }
.rank-3 { background: linear-gradient(45deg, #cd7f32, #daa520); }
.rank-other { background: linear-gradient(45deg, #1890ff, #40a9ff); }

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.metric-item:last-child {
  border-bottom: none;
}

.metric-label {
  color: #666;
  font-size: 14px;
}

.metric-value {
  font-weight: 500;
}

.positive {
  color: #52c41a;
}

.negative {
  color: #ff4d4f;
}

.neutral {
  color: #666;
}

.real-time-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #52c41a;
}

.real-time-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #52c41a;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.7);
  }
  
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(82, 196, 26, 0);
  }
  
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
  }
}

.chart-container {
  height: 300px;
  margin: 16px 0;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.error-container {
  text-align: center;
  padding: 40px;
  color: #ff4d4f;
}

.header-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.header-stat-item {
  flex: 1;
  text-align: center;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 4px;
}

.header-stat-label {
  font-size: 14px;
  color: #666;
}

.position-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin: 16px 0;
}

.position-stat-card {
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.position-percentage {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}

.long-percentage {
  color: #52c41a;
}

.short-percentage {
  color: #ff4d4f;
}

@media (max-width: 768px) {
  .main-content {
    padding: 16px;
  }
  
  .header-stats {
    flex-direction: column;
    gap: 12px;
  }
  
  .position-stats {
    grid-template-columns: 1fr;
  }
}
