import React, { useState, useEffect } from 'react'
import { Card, Row, Col, Select, Typography, Spin, Alert, Progress } from 'antd'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts'
import { useApi } from '../hooks/useApi'
import { useSocket } from '../hooks/useSocket'

const { Title, Text } = Typography
const { Option } = Select

function Statistics() {
  const [timeframe, setTimeframe] = useState('1h')
  const [coinStats, setCoinStats] = useState([])
  const [overallStats, setOverallStats] = useState(null)
  const [historyData, setHistoryData] = useState([])
  
  const { data: statsData, loading: statsLoading, error: statsError } = useApi(`/api/stats/positions?timeframe=${timeframe}`)
  const { data: coinsData, loading: coinsLoading } = useApi(`/api/stats/coins?timeframe=${timeframe}`)
  const { data: historyResponse, loading: historyLoading } = useApi(`/api/stats/history?timeframe=${timeframe}&hours=24`)
  
  const socket = useSocket()

  useEffect(() => {
    if (statsData?.stats) {
      setOverallStats(statsData.stats)
    }
  }, [statsData])

  useEffect(() => {
    if (coinsData?.coins) {
      setCoinStats(coinsData.coins)
    }
  }, [coinsData])

  useEffect(() => {
    if (historyResponse?.history) {
      setHistoryData(historyResponse.history)
    }
  }, [historyResponse])

  useEffect(() => {
    if (socket) {
      socket.on('statsUpdate', (report) => {
        if (report.overall) {
          setOverallStats(report.overall)
        }
        if (report.coins) {
          setCoinStats(report.coins)
        }
      })

      return () => {
        socket.off('statsUpdate')
      }
    }
  }, [socket])

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString('zh-TW', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getPositionColor = (percentage) => {
    if (percentage > 60) return '#52c41a'
    if (percentage > 40) return '#faad14'
    return '#ff4d4f'
  }

  const loading = statsLoading || coinsLoading || historyLoading

  if (loading) {
    return (
      <div className="loading-container">
        <Spin size="large" />
      </div>
    )
  }

  if (statsError) {
    return (
      <div className="error-container">
        <Alert
          message="載入失敗"
          description={statsError}
          type="error"
          showIcon
        />
      </div>
    )
  }

  // 準備圖表數據
  const coinChartData = coinStats.map(coin => ({
    coin: coin.coin,
    longPercentage: coin.long_percentage,
    shortPercentage: coin.short_percentage,
    volume: coin.total_volume,
    activeTraders: coin.active_traders
  }))

  const historyChartData = historyData.map(item => ({
    time: formatTime(item.timestamp),
    longPercentage: item.long_percentage,
    shortPercentage: item.short_percentage,
    activeTraders: item.active_traders
  }))

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2}>開單統計分析</Title>
        <Select
          value={timeframe}
          onChange={setTimeframe}
          style={{ width: 120 }}
        >
          <Option value="1h">1 小時</Option>
          <Option value="4h">4 小時</Option>
          <Option value="24h">24 小時</Option>
        </Select>
      </div>

      {/* 總體統計 */}
      {overallStats && (
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={8}>
            <Card>
              <div style={{ textAlign: 'center' }}>
                <Title level={3} style={{ color: getPositionColor(overallStats.long_percentage), margin: 0 }}>
                  {overallStats.long_percentage?.toFixed(1) || 0}%
                </Title>
                <Text>優秀交易員做多比例</Text>
                <Progress 
                  percent={overallStats.long_percentage || 0} 
                  strokeColor={getPositionColor(overallStats.long_percentage)}
                  showInfo={false}
                  style={{ marginTop: 8 }}
                />
                <div style={{ marginTop: 8 }}>
                  <Text type="secondary">{overallStats.long_positions || 0} 筆做多</Text>
                </div>
              </div>
            </Card>
          </Col>
          
          <Col span={8}>
            <Card>
              <div style={{ textAlign: 'center' }}>
                <Title level={3} style={{ color: getPositionColor(100 - (overallStats.short_percentage || 0)), margin: 0 }}>
                  {overallStats.short_percentage?.toFixed(1) || 0}%
                </Title>
                <Text>優秀交易員做空比例</Text>
                <Progress 
                  percent={overallStats.short_percentage || 0} 
                  strokeColor="#ff4d4f"
                  showInfo={false}
                  style={{ marginTop: 8 }}
                />
                <div style={{ marginTop: 8 }}>
                  <Text type="secondary">{overallStats.short_positions || 0} 筆做空</Text>
                </div>
              </div>
            </Card>
          </Col>
          
          <Col span={8}>
            <Card>
              <div style={{ textAlign: 'center' }}>
                <Title level={3} style={{ color: '#1890ff', margin: 0 }}>
                  {overallStats.active_traders || 0}
                </Title>
                <Text>活躍交易員數量</Text>
                <div style={{ marginTop: 16 }}>
                  <Text type="secondary">
                    總交易量: ${(overallStats.total_volume || 0).toLocaleString()}
                  </Text>
                </div>
              </div>
            </Card>
          </Col>
        </Row>
      )}

      <Row gutter={16}>
        {/* 各幣種開單分佈 */}
        <Col span={12}>
          <Card title="各幣種開單分佈" style={{ height: 400 }}>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={coinChartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="coin" />
                <YAxis />
                <Tooltip 
                  formatter={(value, name) => [
                    `${value.toFixed(1)}%`,
                    name === 'longPercentage' ? '做多比例' : '做空比例'
                  ]}
                />
                <Bar dataKey="longPercentage" fill="#52c41a" name="做多比例" />
                <Bar dataKey="shortPercentage" fill="#ff4d4f" name="做空比例" />
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </Col>

        {/* 歷史趨勢 */}
        <Col span={12}>
          <Card title="24小時趨勢" style={{ height: 400 }}>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={historyChartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="time" />
                <YAxis />
                <Tooltip 
                  formatter={(value, name) => [
                    name === 'activeTraders' ? value : `${value.toFixed(1)}%`,
                    name === 'longPercentage' ? '做多比例' : 
                    name === 'shortPercentage' ? '做空比例' : '活躍交易員'
                  ]}
                />
                <Line 
                  type="monotone" 
                  dataKey="longPercentage" 
                  stroke="#52c41a" 
                  strokeWidth={2}
                  name="做多比例"
                />
                <Line 
                  type="monotone" 
                  dataKey="shortPercentage" 
                  stroke="#ff4d4f" 
                  strokeWidth={2}
                  name="做空比例"
                />
                <Line 
                  type="monotone" 
                  dataKey="activeTraders" 
                  stroke="#1890ff" 
                  strokeWidth={2}
                  name="活躍交易員"
                  yAxisId="right"
                />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      {/* 詳細幣種統計 */}
      <Row style={{ marginTop: 24 }}>
        <Col span={24}>
          <Card title="詳細幣種統計">
            <Row gutter={16}>
              {coinStats.map(coin => (
                <Col span={6} key={coin.coin} style={{ marginBottom: 16 }}>
                  <Card size="small">
                    <div style={{ textAlign: 'center' }}>
                      <Title level={4} style={{ margin: '0 0 8px 0' }}>
                        {coin.coin}
                      </Title>
                      
                      <div style={{ marginBottom: 8 }}>
                        <Text strong className="positive">
                          做多: {coin.long_percentage?.toFixed(1) || 0}%
                        </Text>
                      </div>
                      
                      <div style={{ marginBottom: 8 }}>
                        <Text strong className="negative">
                          做空: {coin.short_percentage?.toFixed(1) || 0}%
                        </Text>
                      </div>
                      
                      <div style={{ marginBottom: 8 }}>
                        <Text type="secondary">
                          活躍: {coin.active_traders || 0} 人
                        </Text>
                      </div>
                      
                      <div>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          交易量: ${(coin.total_volume || 0).toLocaleString()}
                        </Text>
                      </div>
                    </div>
                  </Card>
                </Col>
              ))}
            </Row>
          </Card>
        </Col>
      </Row>

      <div style={{ marginTop: 24 }}>
        <Alert
          message="統計說明"
          description={
            <div>
              <p>• 統計對象：評分排名前 100 的優秀交易員</p>
              <p>• 統計時間：{timeframe === '1h' ? '過去 1 小時' : timeframe === '4h' ? '過去 4 小時' : '過去 24 小時'}內的開單情況</p>
              <p>• 更新頻率：每分鐘自動更新統計數據</p>
              <p>• 數據來源：Hyperliquid 實時交易數據</p>
            </div>
          }
          type="info"
          showIcon
        />
      </div>
    </div>
  )
}

export default Statistics
