import { botConfig, timeframes } from '../config/index.js';
import logger from '../../utils/logger.js';
import database from '../../models/database.js';
import traderEvaluationService from '../../services/traderEvaluation.js';

class TraderCommands {
  constructor(bot) {
    this.bot = bot;
  }

  async top(msg, args) {
    const chatId = msg.chat.id;

    try {
      // 解析參數
      let limit = 10;
      let timeframe = '24h';
      let page = 1;

      if (args.length > 0) {
        const firstArg = args[0];
        if (timeframes[firstArg]) {
          timeframe = firstArg;
        } else if (!isNaN(firstArg)) {
          limit = Math.min(parseInt(firstArg), botConfig.features.maxTopTraders);
        }
      }

      if (args.length > 1) {
        const secondArg = args[1];
        if (timeframes[secondArg]) {
          timeframe = secondArg;
        } else if (!isNaN(secondArg)) {
          page = Math.max(1, parseInt(secondArg));
        }
      }

      // 獲取頂級交易員數據
      const topTraders = await this.getTopTraders(timeframe, limit, page);

      if (!topTraders || topTraders.length === 0) {
        await this.bot.bot.sendMessage(chatId, 
          '📊 暫無交易員數據，請稍後再試。'
        );
        return;
      }

      // 格式化消息
      const message = await this.formatTopTradersMessage(topTraders, timeframe, page);

      // 創建分頁和時間範圍切換鍵盤
      const keyboard = this.createTopTradersKeyboard(timeframe, page, topTraders.length, limit);

      await this.bot.bot.sendMessage(chatId, message, {
        reply_markup: keyboard,
        parse_mode: 'HTML'
      });

    } catch (error) {
      logger.error('Error in top command:', error);
      await this.bot.sendErrorMessage(chatId, '獲取頂級交易員數據失敗。');
    }
  }

  async trader(msg, args) {
    const chatId = msg.chat.id;

    try {
      if (args.length === 0) {
        await this.bot.bot.sendMessage(chatId, 
          '❌ 請提供交易員地址。\n用法: /trader 0x1234...5678'
        );
        return;
      }

      const address = args[0];
      
      // 驗證地址格式
      if (!this.isValidAddress(address)) {
        await this.bot.bot.sendMessage(chatId, 
          '❌ 地址格式不正確，請提供有效的以太坊地址。'
        );
        return;
      }

      // 獲取交易員詳細信息
      const traderInfo = await this.getTraderDetails(address);

      if (!traderInfo) {
        await this.bot.bot.sendMessage(chatId, 
          '❌ 未找到該交易員的數據。'
        );
        return;
      }

      // 格式化消息
      const message = await this.formatTraderDetailsMessage(traderInfo);

      // 創建操作鍵盤
      const keyboard = this.createTraderDetailsKeyboard(address);

      await this.bot.bot.sendMessage(chatId, message, {
        reply_markup: keyboard,
        parse_mode: 'HTML'
      });

    } catch (error) {
      logger.error('Error in trader command:', error);
      await this.bot.sendErrorMessage(chatId, '獲取交易員信息失敗。');
    }
  }

  async search(msg, args) {
    const chatId = msg.chat.id;

    try {
      if (args.length === 0) {
        await this.bot.bot.sendMessage(chatId, 
          '❌ 請提供搜索關鍵詞。\n用法: /search 0x1234'
        );
        return;
      }

      const keyword = args.join(' ');
      
      // 搜索交易員
      const results = await this.searchTraders(keyword);

      if (!results || results.length === 0) {
        await this.bot.bot.sendMessage(chatId, 
          '🔍 未找到匹配的交易員。'
        );
        return;
      }

      // 格式化搜索結果
      const message = this.formatSearchResults(results, keyword);

      // 創建結果選擇鍵盤
      const keyboard = this.createSearchResultsKeyboard(results);

      await this.bot.bot.sendMessage(chatId, message, {
        reply_markup: keyboard,
        parse_mode: 'HTML'
      });

    } catch (error) {
      logger.error('Error in search command:', error);
      await this.bot.sendErrorMessage(chatId, '搜索交易員失敗。');
    }
  }

  async getTopTraders(timeframe, limit, page) {
    try {
      const timeframeSeconds = timeframes[timeframe]?.seconds || 86400;
      const startTime = Date.now() - (timeframeSeconds * 1000);
      const offset = (page - 1) * limit;

      const query = `
        SELECT 
          t.address,
          t.total_trades,
          t.win_rate,
          t.total_pnl,
          t.total_volume,
          t.avg_position_size,
          t.last_trade_time,
          COUNT(tr.id) as recent_trades,
          SUM(CASE WHEN tr.pnl > 0 THEN 1 ELSE 0 END) as recent_wins,
          SUM(tr.pnl) as recent_pnl,
          SUM(tr.size * tr.price) as recent_volume
        FROM traders t
        LEFT JOIN trades tr ON t.address = tr.trader_address 
          AND tr.timestamp > ?
        WHERE t.total_trades >= 10
        GROUP BY t.address
        HAVING recent_trades > 0
        ORDER BY recent_pnl DESC, t.win_rate DESC
        LIMIT ? OFFSET ?
      `;

      const traders = await database.all(query, [startTime, limit, offset]);
      return traders;
    } catch (error) {
      logger.error('Error getting top traders:', error);
      throw error;
    }
  }

  async getTraderDetails(address) {
    try {
      const trader = await database.get(
        'SELECT * FROM traders WHERE address = ?',
        [address]
      );

      if (!trader) return null;

      // 獲取最近交易
      const recentTrades = await database.all(`
        SELECT * FROM trades 
        WHERE trader_address = ? 
        ORDER BY timestamp DESC 
        LIMIT 10
      `, [address]);

      // 獲取持倉信息
      const positions = await database.all(`
        SELECT * FROM positions 
        WHERE trader_address = ? 
        AND is_open = 1
      `, [address]);

      return {
        ...trader,
        recentTrades,
        positions
      };
    } catch (error) {
      logger.error('Error getting trader details:', error);
      throw error;
    }
  }

  async searchTraders(keyword) {
    try {
      const query = `
        SELECT 
          address,
          total_trades,
          win_rate,
          total_pnl,
          total_volume
        FROM traders 
        WHERE address LIKE ? 
        ORDER BY total_pnl DESC
        LIMIT ?
      `;

      const results = await database.all(query, [
        `%${keyword}%`,
        botConfig.features.maxSearchResults
      ]);

      return results;
    } catch (error) {
      logger.error('Error searching traders:', error);
      throw error;
    }
  }

  async formatTopTradersMessage(traders, timeframe, page) {
    const timeLabel = timeframes[timeframe]?.label || '24小時';
    let message = `📊 頂級交易員排行榜 (${timeLabel})\n`;
    message += `📄 第 ${page} 頁\n\n`;

    traders.forEach((trader, index) => {
      const rank = (page - 1) * 10 + index + 1;
      const emoji = rank === 1 ? '🥇' : rank === 2 ? '🥈' : rank === 3 ? '🥉' : `${rank}.`;
      
      const address = `${trader.address.slice(0, 6)}...${trader.address.slice(-4)}`;
      const pnl = trader.recent_pnl || 0;
      const winRate = trader.recent_wins && trader.recent_trades 
        ? ((trader.recent_wins / trader.recent_trades) * 100).toFixed(1)
        : trader.win_rate?.toFixed(1) || '0.0';
      const volume = trader.recent_volume || trader.total_volume || 0;

      message += `${emoji} <code>${address}</code>\n`;
      message += `💰 收益: ${pnl >= 0 ? '+' : ''}${pnl.toFixed(2)} USDC\n`;
      message += `📈 勝率: ${winRate}%\n`;
      message += `💎 交易量: $${volume.toLocaleString()}\n\n`;
    });

    message += `💡 點擊地址查看詳細信息`;
    return message;
  }

  async formatTraderDetailsMessage(traderInfo) {
    const address = traderInfo.address;
    const shortAddress = `${address.slice(0, 6)}...${address.slice(-4)}`;

    let message = `👤 交易員詳細信息\n\n`;
    message += `📍 地址: <code>${address}</code>\n\n`;

    message += `📊 總體統計:\n`;
    message += `• 總交易數: ${traderInfo.total_trades || 0}\n`;
    message += `• 勝率: ${(traderInfo.win_rate || 0).toFixed(1)}%\n`;
    message += `• 總收益: ${(traderInfo.total_pnl || 0).toFixed(2)} USDC\n`;
    message += `• 總交易量: $${(traderInfo.total_volume || 0).toLocaleString()}\n`;
    message += `• 平均倉位: $${(traderInfo.avg_position_size || 0).toLocaleString()}\n\n`;

    if (traderInfo.positions && traderInfo.positions.length > 0) {
      message += `📈 當前持倉:\n`;
      traderInfo.positions.slice(0, 5).forEach(pos => {
        const side = pos.side === 'long' ? '🟢 做多' : '🔴 做空';
        message += `• ${side} ${pos.coin}: $${pos.size.toLocaleString()}\n`;
      });
      message += `\n`;
    }

    if (traderInfo.recentTrades && traderInfo.recentTrades.length > 0) {
      message += `📋 最近交易:\n`;
      traderInfo.recentTrades.slice(0, 3).forEach(trade => {
        const side = trade.side === 'long' ? '🟢' : '🔴';
        const pnl = trade.pnl || 0;
        const pnlText = pnl >= 0 ? `+${pnl.toFixed(2)}` : pnl.toFixed(2);
        const time = new Date(trade.timestamp).toLocaleDateString();
        message += `${side} ${trade.coin} | ${pnlText} USDC | ${time}\n`;
      });
    }

    return message;
  }

  formatSearchResults(results, keyword) {
    let message = `🔍 搜索結果: "${keyword}"\n\n`;

    results.forEach((trader, index) => {
      const address = `${trader.address.slice(0, 6)}...${trader.address.slice(-4)}`;
      const pnl = trader.total_pnl || 0;
      const winRate = trader.win_rate || 0;

      message += `${index + 1}. <code>${address}</code>\n`;
      message += `💰 總收益: ${pnl >= 0 ? '+' : ''}${pnl.toFixed(2)} USDC\n`;
      message += `📈 勝率: ${winRate.toFixed(1)}%\n\n`;
    });

    return message;
  }

  createTopTradersKeyboard(timeframe, page, resultCount, limit) {
    const keyboard = { inline_keyboard: [] };

    // 時間範圍切換
    const timeRow = [];
    Object.keys(timeframes).forEach(tf => {
      const label = tf === timeframe ? `• ${tf} •` : tf;
      timeRow.push({
        text: label,
        callback_data: `top_traders:${tf}:1`
      });
    });
    keyboard.inline_keyboard.push(timeRow);

    // 分頁控制
    const pageRow = [];
    if (page > 1) {
      pageRow.push({
        text: '⬅️ 上一頁',
        callback_data: `top_traders:${timeframe}:${page - 1}`
      });
    }
    if (resultCount === limit) {
      pageRow.push({
        text: '下一頁 ➡️',
        callback_data: `top_traders:${timeframe}:${page + 1}`
      });
    }
    if (pageRow.length > 0) {
      keyboard.inline_keyboard.push(pageRow);
    }

    // 操作按鈕
    keyboard.inline_keyboard.push([
      { text: '🔄 刷新', callback_data: `top_traders:${timeframe}:${page}` },
      { text: '📊 統計', callback_data: `stats:${timeframe}` }
    ]);

    return keyboard;
  }

  createTraderDetailsKeyboard(address) {
    return {
      inline_keyboard: [
        [
          { text: '📈 查看圖表', callback_data: `trader_chart:${address}` },
          { text: '📋 交易記錄', callback_data: `trader_trades:${address}` }
        ],
        [
          { text: '🔔 設置提醒', callback_data: `trader_alert:${address}` },
          { text: '📊 對比分析', callback_data: `trader_compare:${address}` }
        ],
        [
          { text: '🏠 返回排行榜', callback_data: 'top_traders:24h:1' }
        ]
      ]
    };
  }

  createSearchResultsKeyboard(results) {
    const keyboard = { inline_keyboard: [] };

    results.slice(0, 5).forEach((trader, index) => {
      const address = `${trader.address.slice(0, 6)}...${trader.address.slice(-4)}`;
      keyboard.inline_keyboard.push([{
        text: `${index + 1}. ${address}`,
        callback_data: `trader_detail:${trader.address}`
      }]);
    });

    keyboard.inline_keyboard.push([
      { text: '🔍 新搜索', switch_inline_query_current_chat: '/search ' }
    ]);

    return keyboard;
  }

  isValidAddress(address) {
    return /^0x[a-fA-F0-9]{40}$/.test(address);
  }
}

export default TraderCommands;
