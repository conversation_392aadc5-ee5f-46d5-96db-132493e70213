console.log('🚀 Starting debug test...');

try {
  console.log('📦 Loading dotenv...');
  const dotenv = await import('dotenv');
  dotenv.config();
  console.log('✅ dotenv loaded');

  console.log('🔍 Checking environment variables...');
  console.log('TELEGRAM_BOT_TOKEN exists:', !!process.env.TELEGRAM_BOT_TOKEN);
  console.log('Token length:', process.env.TELEGRAM_BOT_TOKEN ? process.env.TELEGRAM_BOT_TOKEN.length : 0);

  console.log('📦 Loading TelegramBot...');
  const TelegramBot = (await import('node-telegram-bot-api')).default;
  console.log('✅ TelegramBot loaded');

  if (!process.env.TELEGRAM_BOT_TOKEN) {
    console.error('❌ No bot token found');
    process.exit(1);
  }

  console.log('🤖 Creating bot instance...');
  const bot = new TelegramBot(process.env.TELEGRAM_BOT_TOKEN, { polling: true });
  console.log('✅ Bot instance created');

  console.log('📡 Getting bot info...');
  const me = await bot.getMe();
  console.log('🎉 Bot info received:', {
    id: me.id,
    username: me.username,
    first_name: me.first_name
  });

  console.log('✅ All tests passed! Bot is working correctly.');
  
  bot.stopPolling();
  process.exit(0);

} catch (error) {
  console.error('❌ Error:', error.message);
  console.error('Stack:', error.stack);
  process.exit(1);
}
