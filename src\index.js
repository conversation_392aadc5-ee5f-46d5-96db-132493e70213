import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import { Server } from 'socket.io';
import http from 'http';
import path from 'path';
import { fileURLToPath } from 'url';

import { config } from './config/index.js';
import logger from './utils/logger.js';
import database from './models/database.js';
import cacheService from './services/cacheService.js';
import DataCollector from './services/dataCollector.js';
import RealTimeStats from './services/realTimeStats.js';
import TraderEvaluator from './services/traderEvaluator.js';
import apiRoutes from './api/routes.js';
import HyperliquidBot from './bot/index.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class HyperliquidTraderTracker {
  constructor() {
    this.app = express();
    this.server = null;
    this.io = null;
    this.dataCollector = null;
    this.realTimeStats = null;
    this.traderEvaluator = null;
    this.telegramBot = null;
    this.isShuttingDown = false;
  }

  async initialize() {
    try {
      logger.info('Initializing Hyperliquid Trader Tracker...');

      // 初始化數據庫
      await database.initialize();
      logger.info('Database initialized');

      // 初始化緩存
      await cacheService.initialize();
      logger.info('Cache service initialized');

      // 設置 Express 應用
      this.setupExpress();

      // 創建 HTTP 服務器和 Socket.IO
      this.server = http.createServer(this.app);
      this.setupSocketIO();

      // 初始化服務
      await this.initializeServices();

      logger.info('Application initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize application:', error);
      throw error;
    }
  }

  setupExpress() {
    // 安全中間件
    this.app.use(helmet());
    
    // CORS 配置
    this.app.use(cors({
      origin: config.server.corsOrigin,
      credentials: true
    }));

    // 壓縮
    this.app.use(compression());

    // JSON 解析
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));

    // 靜態文件服務
    this.app.use(express.static(path.join(__dirname, '../client/dist')));

    // API 路由
    this.app.use('/api', apiRoutes);

    // 健康檢查
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'ok',
        timestamp: Date.now(),
        uptime: process.uptime(),
        services: {
          database: database.isInitialized,
          cache: cacheService.isConnected,
          dataCollector: this.dataCollector?.isRunning || false,
          realTimeStats: this.realTimeStats?.isRunning || false
        }
      });
    });

    // SPA 路由處理
    this.app.get('*', (req, res) => {
      res.sendFile(path.join(__dirname, '../client/dist/index.html'));
    });

    // 錯誤處理
    this.app.use((error, req, res, next) => {
      logger.error('Express error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    });
  }

  setupSocketIO() {
    this.io = new Server(this.server, {
      cors: {
        origin: config.server.corsOrigin,
        methods: ['GET', 'POST']
      }
    });

    this.io.on('connection', (socket) => {
      logger.info(`Client connected: ${socket.id}`);

      // 發送初始數據
      this.sendInitialData(socket);

      // 處理客戶端訂閱
      socket.on('subscribe', (data) => {
        const { type, params } = data;
        socket.join(`${type}:${JSON.stringify(params)}`);
        logger.debug(`Client ${socket.id} subscribed to ${type}`);
      });

      socket.on('unsubscribe', (data) => {
        const { type, params } = data;
        socket.leave(`${type}:${JSON.stringify(params)}`);
        logger.debug(`Client ${socket.id} unsubscribed from ${type}`);
      });

      socket.on('disconnect', () => {
        logger.info(`Client disconnected: ${socket.id}`);
      });
    });
  }

  async sendInitialData(socket) {
    try {
      // 發送最新的統計數據
      const report = await this.realTimeStats?.generateReport('1h');
      if (report) {
        socket.emit('initialData', {
          type: 'report',
          data: report
        });
      }

      // 發送優秀交易員列表
      const topTraders = await this.traderEvaluator?.getRankings('30d', 20);
      if (topTraders) {
        socket.emit('initialData', {
          type: 'topTraders',
          data: topTraders
        });
      }
    } catch (error) {
      logger.error('Error sending initial data:', error);
    }
  }

  async initializeServices() {
    // 創建服務實例
    this.dataCollector = new DataCollector();
    this.realTimeStats = new RealTimeStats();
    this.traderEvaluator = new TraderEvaluator();
    this.telegramBot = new HyperliquidBot();

    // 將服務實例添加到 app.locals 以便在路由中使用
    this.app.locals.dataCollector = this.dataCollector;
    this.app.locals.realTimeStats = this.realTimeStats;
    this.app.locals.traderEvaluator = this.traderEvaluator;
    this.app.locals.telegramBot = this.telegramBot;

    // 設置事件監聽器
    this.setupEventListeners();

    // 啟動服務
    await this.realTimeStats.start();
    await this.dataCollector.start();

    // 啟動 Telegram Bot（如果配置了 token）
    try {
      await this.telegramBot.initialize();
      logger.info('Telegram bot started successfully');
    } catch (error) {
      logger.warn('Telegram bot failed to start:', error.message);
      logger.info('Continuing without Telegram bot...');
    }

    // 執行初始評估
    setTimeout(async () => {
      try {
        await this.traderEvaluator.evaluateAllTraders('30d');
        logger.info('Initial trader evaluation completed');
      } catch (error) {
        logger.error('Initial trader evaluation failed:', error);
      }
    }, 10000); // 10秒後執行

    logger.info('All services initialized and started');
  }

  setupEventListeners() {
    // 數據收集器事件
    this.dataCollector.on('tradeProcessed', (trade) => {
      // 通知實時統計服務
      this.realTimeStats.handleNewTrade(trade);
      
      // 廣播給客戶端
      this.io.emit('newTrade', trade);
    });

    this.dataCollector.on('newTrader', (trader) => {
      this.io.emit('newTrader', trader);
    });

    // 實時統計事件
    this.realTimeStats.on('statsUpdated', async () => {
      try {
        const report = await this.realTimeStats.generateReport('1h');
        this.io.emit('statsUpdate', report);
      } catch (error) {
        logger.error('Error broadcasting stats update:', error);
      }
    });

    this.realTimeStats.on('tradeFromTopTrader', (data) => {
      this.io.emit('topTraderTrade', data);
    });

    this.realTimeStats.on('topTradersUpdated', () => {
      this.io.emit('topTradersUpdated');
    });
  }

  async start() {
    try {
      await this.initialize();

      this.server.listen(config.server.port, () => {
        logger.info(`Server running on port ${config.server.port}`);
        logger.info(`Environment: ${config.server.nodeEnv}`);
        logger.info(`WebSocket server ready`);
      });

      // 優雅關閉處理
      this.setupGracefulShutdown();

    } catch (error) {
      logger.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  setupGracefulShutdown() {
    const shutdown = async (signal) => {
      if (this.isShuttingDown) return;
      this.isShuttingDown = true;

      logger.info(`Received ${signal}, starting graceful shutdown...`);

      try {
        // 停止接受新連接
        this.server.close(() => {
          logger.info('HTTP server closed');
        });

        // 停止服務
        if (this.dataCollector) {
          await this.dataCollector.stop();
        }

        if (this.realTimeStats) {
          await this.realTimeStats.stop();
        }

        if (this.telegramBot) {
          await this.telegramBot.stop();
        }

        // 關閉數據庫連接
        await database.close();

        // 關閉緩存連接
        await cacheService.disconnect();

        logger.info('Graceful shutdown completed');
        process.exit(0);
      } catch (error) {
        logger.error('Error during shutdown:', error);
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));

    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception:', error);
      shutdown('uncaughtException');
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
      shutdown('unhandledRejection');
    });
  }
}

// 啟動應用
const app = new HyperliquidTraderTracker();
app.start().catch(error => {
  logger.error('Failed to start application:', error);
  process.exit(1);
});

export default app;
