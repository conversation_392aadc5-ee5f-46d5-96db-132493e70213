import dotenv from 'dotenv';
import TelegramBot from 'node-telegram-bot-api';

dotenv.config();

console.log('🚀 Starting Hyperliquid Telegram Bot...');

async function startBot() {
  try {
    // 檢查 Bot Token
    if (!process.env.TELEGRAM_BOT_TOKEN) {
      console.error('❌ TELEGRAM_BOT_TOKEN not found in .env file');
      process.exit(1);
    }

    console.log('✅ Bot token found');

    // 創建 Bot 實例
    const bot = new TelegramBot(process.env.TELEGRAM_BOT_TOKEN, { 
      polling: true
    });

    console.log('🎉 Telegram Bot created successfully!');

    // 獲取 Bot 信息
    const me = await bot.getMe();
    console.log('🤖 Bot info:', {
      id: me.id,
      username: me.username,
      first_name: me.first_name
    });

    console.log('🚀 Bot is now running and ready to receive messages!');
    console.log('💬 Send /start to your bot to test it');
    console.log('🛑 Press Ctrl+C to stop the bot');

    // 設置指令處理
    bot.onText(/\/start/, async (msg) => {
      const chatId = msg.chat.id;
      const username = msg.from.username || msg.from.first_name;
      
      console.log(`📨 Received /start from: ${username} (${msg.from.id})`);
      
      try {
        await bot.sendMessage(chatId, 
          '🎉 歡迎使用 Hyperliquid 交易員追蹤機器人！\n\n' +
          '🚀 這是一個專業的交易員分析工具，可以幫助您:\n' +
          '• 📊 追蹤優秀交易員的表現\n' +
          '• 📈 分析市場趨勢和統計數據\n' +
          '• 🔔 接收重要交易提醒\n\n' +
          '使用 /help 查看所有可用指令。',
          { parse_mode: 'HTML' }
        );
        console.log(`✅ Sent welcome message to ${username}`);
      } catch (error) {
        console.error('❌ Error sending welcome message:', error.message);
      }
    });

    bot.onText(/\/help/, async (msg) => {
      const chatId = msg.chat.id;
      const username = msg.from.username || msg.from.first_name;
      
      console.log(`📨 Received /help from: ${username}`);
      
      try {
        await bot.sendMessage(chatId, 
          '📚 <b>Hyperliquid 交易員追蹤機器人指令</b>\n\n' +
          '🔧 <b>基本指令:</b>\n' +
          '• /start - 開始使用機器人\n' +
          '• /help - 顯示此幫助信息\n' +
          '• /status - 查看系統狀態\n' +
          '• /ping - 測試響應速度\n\n' +
          '📊 <b>查詢指令 (開發中):</b>\n' +
          '• /top - 查看頂級交易員\n' +
          '• /stats - 查看交易統計\n' +
          '• /trader &lt;地址&gt; - 查看特定交易員\n\n' +
          '🔔 <b>通知指令 (開發中):</b>\n' +
          '• /alerts - 管理通知設置\n' +
          '• /subscribe - 訂閱通知\n' +
          '• /unsubscribe - 取消訂閱\n\n' +
          '💡 完整功能正在開發中...',
          { parse_mode: 'HTML' }
        );
        console.log(`✅ Sent help message to ${username}`);
      } catch (error) {
        console.error('❌ Error sending help message:', error.message);
      }
    });

    bot.onText(/\/ping/, async (msg) => {
      const chatId = msg.chat.id;
      const username = msg.from.username || msg.from.first_name;
      
      console.log(`📨 Received /ping from: ${username}`);
      
      try {
        const start = Date.now();
        await bot.sendMessage(chatId, 
          '🏓 <b>Pong!</b>\n\n' +
          `⚡ 響應時間: <code>${Date.now() - start}ms</code>\n` +
          `🤖 機器人狀態: <b>正常運行</b>\n` +
          `📅 時間: <code>${new Date().toLocaleString()}</code>`,
          { parse_mode: 'HTML' }
        );
        console.log(`✅ Sent pong to ${username} (${Date.now() - start}ms)`);
      } catch (error) {
        console.error('❌ Error sending pong:', error.message);
      }
    });

    bot.onText(/\/status/, async (msg) => {
      const chatId = msg.chat.id;
      const username = msg.from.username || msg.from.first_name;
      
      console.log(`📨 Received /status from: ${username}`);
      
      try {
        const uptime = process.uptime();
        const uptimeStr = `${Math.floor(uptime / 3600)}h ${Math.floor((uptime % 3600) / 60)}m ${Math.floor(uptime % 60)}s`;
        
        await bot.sendMessage(chatId, 
          '📊 <b>系統狀態報告</b>\n\n' +
          `🤖 機器人: <b>運行中</b>\n` +
          `📡 連接狀態: <b>正常</b>\n` +
          `🕐 運行時間: <code>${uptimeStr}</code>\n` +
          `💾 內存使用: <code>${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB</code>\n` +
          `📅 當前時間: <code>${new Date().toLocaleString()}</code>\n\n` +
          `🔧 版本: <b>測試版 v1.0</b>\n` +
          `📈 狀態: <b>開發中</b>`,
          { parse_mode: 'HTML' }
        );
        console.log(`✅ Sent status to ${username}`);
      } catch (error) {
        console.error('❌ Error sending status:', error.message);
      }
    });

    // 處理未知指令
    bot.onText(/^\/(.+)/, async (msg, match) => {
      const command = match[1];
      const chatId = msg.chat.id;
      const username = msg.from.username || msg.from.first_name;
      
      // 跳過已知指令
      if (['start', 'help', 'ping', 'status'].includes(command)) {
        return;
      }
      
      console.log(`📨 Received unknown command /${command} from: ${username}`);
      
      try {
        await bot.sendMessage(chatId, 
          `❓ 未知指令: <code>/${command}</code>\n\n` +
          '請使用 /help 查看所有可用指令。',
          { parse_mode: 'HTML' }
        );
      } catch (error) {
        console.error('❌ Error sending unknown command response:', error.message);
      }
    });

    // 處理普通消息
    bot.on('message', async (msg) => {
      // 跳過指令消息
      if (msg.text && msg.text.startsWith('/')) {
        return;
      }
      
      const chatId = msg.chat.id;
      const username = msg.from.username || msg.from.first_name;
      
      console.log(`📨 Received message from ${username}: ${msg.text}`);
      
      try {
        await bot.sendMessage(chatId, 
          '🤖 我收到了您的消息！\n\n' +
          `您說: "<i>${msg.text}</i>"\n\n` +
          '請使用 /help 查看可用指令，或直接使用指令與我互動。',
          { parse_mode: 'HTML' }
        );
      } catch (error) {
        console.error('❌ Error sending message response:', error.message);
      }
    });

    // 錯誤處理
    bot.on('error', (error) => {
      console.error('❌ Bot error:', error.message);
    });

    bot.on('polling_error', (error) => {
      console.error('❌ Polling error:', error.message);
      if (error.message.includes('409')) {
        console.log('💡 提示: 確保沒有其他機器人實例在運行');
        console.log('💡 如果問題持續，請等待幾分鐘後重試');
      }
    });

    // 優雅關閉
    process.on('SIGINT', () => {
      console.log('\n🛑 正在關閉機器人...');
      try {
        bot.stopPolling();
        console.log('✅ 機器人已停止');
      } catch (error) {
        console.error('❌ 關閉時發生錯誤:', error.message);
      }
      process.exit(0);
    });

    process.on('SIGTERM', () => {
      console.log('\n🛑 收到終止信號，正在關閉...');
      try {
        bot.stopPolling();
        console.log('✅ 機器人已停止');
      } catch (error) {
        console.error('❌ 關閉時發生錯誤:', error.message);
      }
      process.exit(0);
    });

  } catch (error) {
    console.error('❌ 啟動機器人失敗:', error.message);
    console.error('Stack:', error.stack);
    process.exit(1);
  }
}

// 啟動機器人
startBot().catch(error => {
  console.error('❌ 未處理的錯誤:', error.message);
  console.error('Stack:', error.stack);
  process.exit(1);
});
