import dotenv from 'dotenv';
import TelegramBot from 'node-telegram-bot-api';
import database from './src/models/database.js';
import TraderEvaluator from './src/services/traderEvaluator.js';
import RealTimeStats from './src/services/realTimeStats.js';
import logger from './src/utils/logger.js';

dotenv.config();

console.log('🚀 Starting Hyperliquid Telegram Bot...');

async function startBot() {
  try {
    // 檢查 Bot Token
    if (!process.env.TELEGRAM_BOT_TOKEN) {
      console.error('❌ TELEGRAM_BOT_TOKEN not found in .env file');
      process.exit(1);
    }

    console.log('✅ Bot token found');

    // 初始化數據庫
    try {
      await database.initialize();
      console.log('✅ Database initialized');
    } catch (error) {
      console.error('❌ Database initialization failed:', error.message);
      process.exit(1);
    }

    // 初始化服務
    const traderEvaluator = new TraderEvaluator();
    const realTimeStats = new RealTimeStats();
    console.log('✅ Services initialized');

    // 創建 Bot 實例
    const bot = new TelegramBot(process.env.TELEGRAM_BOT_TOKEN, { 
      polling: true
    });

    console.log('🎉 Telegram Bot created successfully!');

    // 獲取 Bot 信息
    const me = await bot.getMe();
    console.log('🤖 Bot info:', {
      id: me.id,
      username: me.username,
      first_name: me.first_name
    });

    console.log('🚀 Bot is now running and ready to receive messages!');
    console.log('💬 Send /start to your bot to test it');
    console.log('🛑 Press Ctrl+C to stop the bot');

    // 設置指令處理
    bot.onText(/\/start/, async (msg) => {
      const chatId = msg.chat.id;
      const username = msg.from.username || msg.from.first_name;
      
      console.log(`📨 Received /start from: ${username} (${msg.from.id})`);
      
      try {
        await bot.sendMessage(chatId, 
          '🎉 歡迎使用 Hyperliquid 交易員追蹤機器人！\n\n' +
          '🚀 這是一個專業的交易員分析工具，可以幫助您:\n' +
          '• 📊 追蹤優秀交易員的表現\n' +
          '• 📈 分析市場趨勢和統計數據\n' +
          '• 🔔 接收重要交易提醒\n\n' +
          '使用 /help 查看所有可用指令。',
          { parse_mode: 'HTML' }
        );
        console.log(`✅ Sent welcome message to ${username}`);
      } catch (error) {
        console.error('❌ Error sending welcome message:', error.message);
      }
    });

    bot.onText(/\/help/, async (msg) => {
      const chatId = msg.chat.id;
      const username = msg.from.username || msg.from.first_name;
      
      console.log(`📨 Received /help from: ${username}`);
      
      try {
        await bot.sendMessage(chatId, 
          '📚 <b>Hyperliquid 交易員追蹤機器人指令</b>\n\n' +
          '🔧 <b>基本指令:</b>\n' +
          '• /start - 開始使用機器人\n' +
          '• /help - 顯示此幫助信息\n' +
          '• /status - 查看系統狀態\n' +
          '• /ping - 測試響應速度\n\n' +
          '📊 <b>查詢指令 (開發中):</b>\n' +
          '• /top - 查看頂級交易員\n' +
          '• /stats - 查看交易統計\n' +
          '• /trader &lt;地址&gt; - 查看特定交易員\n\n' +
          '🔔 <b>通知指令 (開發中):</b>\n' +
          '• /alerts - 管理通知設置\n' +
          '• /subscribe - 訂閱通知\n' +
          '• /unsubscribe - 取消訂閱\n\n' +
          '💡 完整功能正在開發中...',
          { parse_mode: 'HTML' }
        );
        console.log(`✅ Sent help message to ${username}`);
      } catch (error) {
        console.error('❌ Error sending help message:', error.message);
      }
    });

    bot.onText(/\/ping/, async (msg) => {
      const chatId = msg.chat.id;
      const username = msg.from.username || msg.from.first_name;
      
      console.log(`📨 Received /ping from: ${username}`);
      
      try {
        const start = Date.now();
        await bot.sendMessage(chatId, 
          '🏓 <b>Pong!</b>\n\n' +
          `⚡ 響應時間: <code>${Date.now() - start}ms</code>\n` +
          `🤖 機器人狀態: <b>正常運行</b>\n` +
          `📅 時間: <code>${new Date().toLocaleString()}</code>`,
          { parse_mode: 'HTML' }
        );
        console.log(`✅ Sent pong to ${username} (${Date.now() - start}ms)`);
      } catch (error) {
        console.error('❌ Error sending pong:', error.message);
      }
    });

    bot.onText(/\/status/, async (msg) => {
      const chatId = msg.chat.id;
      const username = msg.from.username || msg.from.first_name;

      console.log(`📨 Received /status from: ${username}`);

      try {
        const uptime = process.uptime();
        const uptimeStr = `${Math.floor(uptime / 3600)}h ${Math.floor((uptime % 3600) / 60)}m ${Math.floor(uptime % 60)}s`;

        // 獲取數據庫統計
        const totalTraders = await database.get('SELECT COUNT(*) as count FROM traders');
        const totalTrades = await database.get('SELECT COUNT(*) as count FROM trades');
        const activeTraders = await database.get(
          'SELECT COUNT(DISTINCT trader_address) as count FROM trades WHERE timestamp >= ?',
          [Date.now() - (24 * 60 * 60 * 1000)]
        );

        await bot.sendMessage(chatId,
          '📊 <b>系統狀態報告</b>\n\n' +
          `🤖 機器人: <b>運行中</b>\n` +
          `📡 連接狀態: <b>正常</b>\n` +
          `💾 數據庫: <b>已連接</b>\n` +
          `🕐 運行時間: <code>${uptimeStr}</code>\n` +
          `💾 內存使用: <code>${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB</code>\n\n` +
          `📈 <b>數據統計:</b>\n` +
          `👥 總交易員: <code>${totalTraders?.count || 0}</code>\n` +
          `📊 總交易數: <code>${totalTrades?.count || 0}</code>\n` +
          `🔥 活躍交易員(24h): <code>${activeTraders?.count || 0}</code>\n\n` +
          `📅 當前時間: <code>${new Date().toLocaleString()}</code>`,
          { parse_mode: 'HTML' }
        );
        console.log(`✅ Sent status to ${username}`);
      } catch (error) {
        console.error('❌ Error sending status:', error.message);
        await bot.sendMessage(chatId, '❌ 獲取系統狀態時發生錯誤');
      }
    });

    // /top 指令 - 查看頂級交易員
    bot.onText(/\/top(?:\s+(\w+))?(?:\s+(\d+))?/, async (msg, match) => {
      const chatId = msg.chat.id;
      const username = msg.from.username || msg.from.first_name;
      const timeframe = match[1] || '24h';
      const limit = parseInt(match[2]) || 10;

      console.log(`📨 Received /top from: ${username}, timeframe: ${timeframe}, limit: ${limit}`);

      try {
        // 發送處理中消息
        const processingMsg = await bot.sendMessage(chatId, '⏳ 正在獲取頂級交易員數據...');

        // 獲取排名數據
        const rankings = await traderEvaluator.getRankings(timeframe, limit);

        if (!rankings || rankings.length === 0) {
          await bot.editMessageText(
            `❌ 暫無 ${timeframe} 時間範圍內的交易員數據`,
            { chat_id: chatId, message_id: processingMsg.message_id }
          );
          return;
        }

        // 格式化排名消息
        let message = `🏆 <b>頂級交易員排名 (${timeframe})</b>\n\n`;

        rankings.slice(0, limit).forEach((trader, index) => {
          const rank = index + 1;
          const emoji = rank === 1 ? '🥇' : rank === 2 ? '🥈' : rank === 3 ? '🥉' : '📈';
          const winRate = (trader.win_rate * 100).toFixed(1);
          const pnl = trader.total_pnl > 0 ? `+$${trader.total_pnl.toFixed(2)}` : `$${trader.total_pnl.toFixed(2)}`;

          message += `${emoji} <b>#${rank}</b>\n`;
          message += `📍 <code>${trader.address.slice(0, 8)}...${trader.address.slice(-6)}</code>\n`;
          message += `💰 PnL: <b>${pnl}</b>\n`;
          message += `🎯 勝率: <b>${winRate}%</b>\n`;
          message += `📊 交易數: <b>${trader.total_trades}</b>\n`;
          message += `⭐ 評分: <b>${trader.performance_score.toFixed(2)}</b>\n\n`;
        });

        message += `📝 使用 /trader &lt;地址&gt; 查看詳細信息\n`;
        message += `💡 支援時間範圍: 1h, 4h, 24h, 7d, 30d`;

        await bot.editMessageText(message, {
          chat_id: chatId,
          message_id: processingMsg.message_id,
          parse_mode: 'HTML'
        });

        console.log(`✅ Sent top traders to ${username}`);
      } catch (error) {
        console.error('❌ Error getting top traders:', error.message);
        await bot.sendMessage(chatId, '❌ 獲取交易員排名時發生錯誤，請稍後再試');
      }
    });

    // /trader 指令 - 查看特定交易員
    bot.onText(/\/trader(?:\s+([a-fA-F0-9x]+))?(?:\s+(\w+))?/, async (msg, match) => {
      const chatId = msg.chat.id;
      const username = msg.from.username || msg.from.first_name;
      const address = match[1];
      const timeframe = match[2] || '30d';

      console.log(`📨 Received /trader from: ${username}, address: ${address}, timeframe: ${timeframe}`);

      if (!address) {
        await bot.sendMessage(chatId,
          '❓ 請提供交易員地址\n\n' +
          '使用方法: <code>/trader &lt;地址&gt; [時間範圍]</code>\n' +
          '例如: <code>/trader 0x1234...abcd 24h</code>',
          { parse_mode: 'HTML' }
        );
        return;
      }

      try {
        const processingMsg = await bot.sendMessage(chatId, '⏳ 正在獲取交易員數據...');

        // 獲取交易員基本信息
        const trader = await database.get('SELECT * FROM traders WHERE address = ?', [address]);

        if (!trader) {
          await bot.editMessageText(
            '❌ 未找到該交易員\n\n請檢查地址是否正確',
            { chat_id: chatId, message_id: processingMsg.message_id }
          );
          return;
        }

        // 獲取評估信息
        const evaluation = await traderEvaluator.getTraderEvaluation(address, timeframe);

        // 獲取最近交易
        const recentTrades = await database.all(
          'SELECT * FROM trades WHERE trader_address = ? ORDER BY timestamp DESC LIMIT 5',
          [address]
        );

        // 格式化消息
        const winRate = (trader.win_rate * 100).toFixed(1);
        const pnl = trader.total_pnl > 0 ? `+$${trader.total_pnl.toFixed(2)}` : `$${trader.total_pnl.toFixed(2)}`;
        const volume = `$${(trader.total_volume / 1000000).toFixed(2)}M`;

        let message = `👤 <b>交易員詳情</b>\n\n`;
        message += `📍 地址: <code>${address}</code>\n`;
        message += `💰 總 PnL: <b>${pnl}</b>\n`;
        message += `📊 總交易量: <b>${volume}</b>\n`;
        message += `🎯 勝率: <b>${winRate}%</b>\n`;
        message += `📈 交易數: <b>${trader.total_trades}</b>\n`;
        message += `⭐ 評分: <b>${trader.performance_score.toFixed(2)}</b>\n\n`;

        if (evaluation) {
          message += `📊 <b>${timeframe} 表現:</b>\n`;
          message += `💵 期間 PnL: <b>$${evaluation.pnl?.toFixed(2) || '0.00'}</b>\n`;
          message += `📊 期間交易: <b>${evaluation.trades || 0}</b>\n`;
          message += `🎯 期間勝率: <b>${(evaluation.winRate * 100).toFixed(1)}%</b>\n\n`;
        }

        if (recentTrades.length > 0) {
          message += `🕐 <b>最近交易:</b>\n`;
          recentTrades.forEach(trade => {
            const side = trade.side === 'buy' ? '🟢 買入' : '🔴 賣出';
            const time = new Date(trade.timestamp).toLocaleString();
            message += `${side} ${trade.coin} $${trade.size.toFixed(2)} (${time})\n`;
          });
        }

        await bot.editMessageText(message, {
          chat_id: chatId,
          message_id: processingMsg.message_id,
          parse_mode: 'HTML'
        });

        console.log(`✅ Sent trader info to ${username}`);
      } catch (error) {
        console.error('❌ Error getting trader info:', error.message);
        await bot.sendMessage(chatId, '❌ 獲取交易員信息時發生錯誤，請稍後再試');
      }
    });

    // /stats 指令 - 查看統計數據
    bot.onText(/\/stats(?:\s+(\w+))?/, async (msg, match) => {
      const chatId = msg.chat.id;
      const username = msg.from.username || msg.from.first_name;
      const timeframe = match[1] || '1h';

      console.log(`📨 Received /stats from: ${username}, timeframe: ${timeframe}`);

      try {
        const processingMsg = await bot.sendMessage(chatId, '⏳ 正在生成統計報告...');

        // 獲取統計報告
        const report = await realTimeStats.generateReport(timeframe);

        if (!report) {
          await bot.editMessageText(
            `❌ 暫無 ${timeframe} 時間範圍內的統計數據`,
            { chat_id: chatId, message_id: processingMsg.message_id }
          );
          return;
        }

        let message = `📊 <b>市場統計報告 (${timeframe})</b>\n\n`;

        // 總體統計
        if (report.summary) {
          message += `📈 <b>總體數據:</b>\n`;
          message += `👥 活躍交易員: <b>${report.summary.activeTraders || 0}</b>\n`;
          message += `📊 總交易數: <b>${report.summary.totalTrades || 0}</b>\n`;
          message += `💰 總交易量: <b>$${((report.summary.totalVolume || 0) / 1000000).toFixed(2)}M</b>\n\n`;
        }

        // 熱門幣種
        if (report.topCoins && report.topCoins.length > 0) {
          message += `🔥 <b>熱門幣種:</b>\n`;
          report.topCoins.slice(0, 5).forEach((coin, index) => {
            const emoji = ['🥇', '🥈', '🥉', '4️⃣', '5️⃣'][index];
            message += `${emoji} ${coin.symbol}: $${(coin.volume / 1000000).toFixed(2)}M\n`;
          });
          message += '\n';
        }

        // 市場趨勢
        if (report.trends) {
          message += `📈 <b>市場趨勢:</b>\n`;
          message += `🟢 看多比例: <b>${(report.trends.bullishRatio * 100).toFixed(1)}%</b>\n`;
          message += `🔴 看空比例: <b>${(report.trends.bearishRatio * 100).toFixed(1)}%</b>\n\n`;
        }

        message += `🕐 更新時間: ${new Date().toLocaleString()}\n`;
        message += `💡 支援時間範圍: 1h, 4h, 24h, 7d, 30d`;

        await bot.editMessageText(message, {
          chat_id: chatId,
          message_id: processingMsg.message_id,
          parse_mode: 'HTML'
        });

        console.log(`✅ Sent stats to ${username}`);
      } catch (error) {
        console.error('❌ Error getting stats:', error.message);
        await bot.sendMessage(chatId, '❌ 獲取統計數據時發生錯誤，請稍後再試');
      }
    });

    // 處理未知指令
    bot.onText(/^\/(.+)/, async (msg, match) => {
      const command = match[1].split(' ')[0]; // 只取指令部分，忽略參數
      const chatId = msg.chat.id;
      const username = msg.from.username || msg.from.first_name;

      // 跳過已知指令
      if (['start', 'help', 'ping', 'status', 'top', 'trader', 'stats'].includes(command)) {
        return;
      }

      console.log(`📨 Received unknown command /${command} from: ${username}`);

      try {
        await bot.sendMessage(chatId,
          `❓ 未知指令: <code>/${command}</code>\n\n` +
          '請使用 /help 查看所有可用指令。',
          { parse_mode: 'HTML' }
        );
      } catch (error) {
        console.error('❌ Error sending unknown command response:', error.message);
      }
    });

    // 處理普通消息
    bot.on('message', async (msg) => {
      // 跳過指令消息
      if (msg.text && msg.text.startsWith('/')) {
        return;
      }
      
      const chatId = msg.chat.id;
      const username = msg.from.username || msg.from.first_name;
      
      console.log(`📨 Received message from ${username}: ${msg.text}`);
      
      try {
        await bot.sendMessage(chatId, 
          '🤖 我收到了您的消息！\n\n' +
          `您說: "<i>${msg.text}</i>"\n\n` +
          '請使用 /help 查看可用指令，或直接使用指令與我互動。',
          { parse_mode: 'HTML' }
        );
      } catch (error) {
        console.error('❌ Error sending message response:', error.message);
      }
    });

    // 錯誤處理
    bot.on('error', (error) => {
      console.error('❌ Bot error:', error.message);
    });

    bot.on('polling_error', (error) => {
      console.error('❌ Polling error:', error.message);
      if (error.message.includes('409')) {
        console.log('💡 提示: 確保沒有其他機器人實例在運行');
        console.log('💡 如果問題持續，請等待幾分鐘後重試');
      }
    });

    // 優雅關閉
    process.on('SIGINT', () => {
      console.log('\n🛑 正在關閉機器人...');
      try {
        bot.stopPolling();
        console.log('✅ 機器人已停止');
      } catch (error) {
        console.error('❌ 關閉時發生錯誤:', error.message);
      }
      process.exit(0);
    });

    process.on('SIGTERM', () => {
      console.log('\n🛑 收到終止信號，正在關閉...');
      try {
        bot.stopPolling();
        console.log('✅ 機器人已停止');
      } catch (error) {
        console.error('❌ 關閉時發生錯誤:', error.message);
      }
      process.exit(0);
    });

  } catch (error) {
    console.error('❌ 啟動機器人失敗:', error.message);
    console.error('Stack:', error.stack);
    process.exit(1);
  }
}

// 啟動機器人
startBot().catch(error => {
  console.error('❌ 未處理的錯誤:', error.message);
  console.error('Stack:', error.stack);
  process.exit(1);
});
