# Hyperliquid 交易員追蹤系統

一個實時篩選優秀 Hyperliquid 交易員並統計開單情況的系統。

## 功能特點

- 🔍 **實時交易員篩選**: 基於多項指標自動識別優秀交易員
- 📊 **開單統計**: 實時統計優秀交易員的開單行為
- 📈 **績效分析**: 詳細的交易員績效評估和排名
- ⚡ **實時監控**: WebSocket 實時數據更新
- 🎯 **智能篩選**: 可配置的篩選條件和評估算法
- 📱 **響應式界面**: 現代化的 Web 界面

## 系統架構

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Hyperliquid   │    │   數據收集層    │    │   數據存儲層    │
│      API        │◄──►│                 │◄──►│                 │
│                 │    │  - WebSocket    │    │  - SQLite       │
│                 │    │  - REST API     │    │  - Redis Cache  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web 界面      │    │   分析引擎      │    │   實時統計      │
│                 │◄──►│                 │◄──►│                 │
│  - 交易員排名   │    │  - 績效評估     │    │  - 開單統計     │
│  - 實時統計     │    │  - 風險分析     │    │  - 趨勢分析     │
│  - 圖表展示     │    │  - 篩選算法     │    │  - 實時更新     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 快速開始

### 環境要求

- Node.js >= 18.0.0
- Redis (可選，用於緩存)

### 安裝

1. 克隆項目
```bash
git clone <repository-url>
cd hyperliquid-trader-tracker
```

2. 安裝依賴
```bash
npm install
```

3. 配置環境變量
```bash
cp .env.example .env
# 編輯 .env 文件設置您的配置
```

4. 啟動服務
```bash
# 開發模式
npm run dev

# 生產模式
npm start
```

5. 訪問應用
打開瀏覽器訪問 `http://localhost:3000`

## 配置說明

### 交易員評估指標

系統使用以下指標來評估交易員表現：

- **勝率**: 盈利交易占總交易的比例
- **收益率**: 總收益與初始資金的比例
- **夏普比率**: 風險調整後的收益率
- **最大回撤**: 最大虧損幅度
- **交易頻率**: 交易活躍度
- **持倉管理**: 風險控制能力

### 篩選條件

可以通過配置文件調整篩選條件：

- 最小交易次數
- 評估時間週期
- 最小資金規模
- 風險控制要求

## API 文檔

### 獲取優秀交易員列表
```
GET /api/traders/top
```

### 獲取實時開單統計
```
GET /api/stats/positions
```

### 獲取交易員詳細信息
```
GET /api/traders/:address
```

## 開發

### 項目結構
```
src/
├── api/           # API 路由
├── services/      # 業務邏輯
├── models/        # 數據模型
├── utils/         # 工具函數
├── config/        # 配置文件
└── index.js       # 入口文件

client/            # 前端代碼
├── src/
├── public/
└── package.json

data/              # 數據文件
logs/              # 日誌文件
```

### 運行測試
```bash
npm test
```

### 代碼格式化
```bash
npm run format
```

## 部署

### Docker 部署
```bash
docker build -t hyperliquid-tracker .
docker run -p 3000:3000 hyperliquid-tracker
```

### PM2 部署
```bash
npm install -g pm2
pm2 start ecosystem.config.js
```

## 貢獻

歡迎提交 Issue 和 Pull Request！

## 許可證

MIT License
