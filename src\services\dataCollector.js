import EventEmitter from 'events';
import HyperliquidAPI from './hyperliquidApi.js';
import database from '../models/database.js';
import { config } from '../config/index.js';
import logger from '../utils/logger.js';

class DataCollector extends EventEmitter {
  constructor() {
    super();
    this.api = new HyperliquidAPI();
    this.isRunning = false;
    this.trackedTraders = new Set();
    this.collectionInterval = null;
    this.positionUpdateInterval = null;
    
    this.setupEventHandlers();
  }

  setupEventHandlers() {
    // WebSocket 事件處理
    this.api.on('connected', () => {
      logger.info('Data collector connected to Hyperliquid API');
      this.subscribeToMarketData();
    });

    this.api.on('trades', (data) => {
      this.handleTradeData(data);
    });

    this.api.on('userEvents', (data) => {
      this.handleUserEvents(data);
    });

    this.api.on('error', (error) => {
      logger.error('Hyperliquid API error:', error);
    });
  }

  async start() {
    if (this.isRunning) {
      logger.warn('Data collector is already running');
      return;
    }

    try {
      logger.info('Starting data collector...');
      
      // 初始化數據庫
      await database.initialize();
      
      // 連接到 Hyperliquid API
      this.api.connect();
      
      // 開始定期數據收集
      this.startPeriodicCollection();
      
      this.isRunning = true;
      logger.info('Data collector started successfully');
      
      this.emit('started');
    } catch (error) {
      logger.error('Error starting data collector:', error);
      throw error;
    }
  }

  async stop() {
    if (!this.isRunning) return;

    logger.info('Stopping data collector...');
    
    // 停止定期收集
    if (this.collectionInterval) {
      clearInterval(this.collectionInterval);
      this.collectionInterval = null;
    }
    
    if (this.positionUpdateInterval) {
      clearInterval(this.positionUpdateInterval);
      this.positionUpdateInterval = null;
    }
    
    // 斷開 API 連接
    this.api.disconnect();
    
    this.isRunning = false;
    logger.info('Data collector stopped');
    
    this.emit('stopped');
  }

  startPeriodicCollection() {
    // 定期收集交易員數據
    this.collectionInterval = setInterval(async () => {
      try {
        await this.collectTraderData();
      } catch (error) {
        logger.error('Error in periodic trader data collection:', error);
      }
    }, config.stats.updateIntervalMs);

    // 定期更新持倉信息
    this.positionUpdateInterval = setInterval(async () => {
      try {
        await this.updatePositions();
      } catch (error) {
        logger.error('Error in periodic position update:', error);
      }
    }, config.stats.positionTrackingIntervalMs);
  }

  subscribeToMarketData() {
    // 訂閱主要幣種的交易數據
    for (const coin of config.supportedCoins) {
      this.api.subscribeTrades(coin);
    }
  }

  async handleTradeData(tradeData) {
    try {
      // 處理交易數據並存儲
      for (const trade of tradeData) {
        await this.processTrade(trade);
      }
    } catch (error) {
      logger.error('Error handling trade data:', error);
    }
  }

  async processTrade(trade) {
    try {
      // 檢查是否是新的交易員
      const trader = await this.getOrCreateTrader(trade.user);
      
      // 存儲交易記錄
      await this.storeTrade(trade);
      
      // 更新交易員統計
      await this.updateTraderStats(trade.user);
      
      // 添加到追蹤列表
      this.trackedTraders.add(trade.user);
      
      this.emit('tradeProcessed', trade);
    } catch (error) {
      logger.error('Error processing trade:', error);
    }
  }

  async getOrCreateTrader(address) {
    try {
      let trader = await database.get(
        'SELECT * FROM traders WHERE address = ?',
        [address]
      );

      if (!trader) {
        // 創建新交易員記錄
        const now = Date.now();
        await database.run(
          `INSERT INTO traders (
            address, first_seen, last_updated, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?)`,
          [address, now, now, now, now]
        );

        trader = await database.get(
          'SELECT * FROM traders WHERE address = ?',
          [address]
        );

        logger.info(`New trader discovered: ${address}`);
        this.emit('newTrader', trader);
      }

      return trader;
    } catch (error) {
      logger.error('Error getting or creating trader:', error);
      throw error;
    }
  }

  async storeTrade(trade) {
    try {
      await database.run(
        `INSERT OR IGNORE INTO trades (
          trader_address, coin, side, size, price, timestamp,
          pnl, fee, hash, oid, tid, crossed, direction, start_position
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          trade.user,
          trade.coin,
          trade.side,
          parseFloat(trade.sz),
          parseFloat(trade.px),
          trade.time,
          parseFloat(trade.closedPnl || 0),
          parseFloat(trade.fee || 0),
          trade.hash,
          trade.oid,
          trade.tid,
          trade.crossed ? 1 : 0,
          trade.dir,
          parseFloat(trade.startPosition || 0)
        ]
      );
    } catch (error) {
      logger.error('Error storing trade:', error);
      throw error;
    }
  }

  async updateTraderStats(address) {
    try {
      // 計算交易員統計數據
      const stats = await this.calculateTraderStats(address);
      
      // 更新交易員記錄
      await database.run(
        `UPDATE traders SET
          total_trades = ?, total_volume = ?, total_pnl = ?,
          win_rate = ?, sharpe_ratio = ?, max_drawdown = ?,
          avg_position_size = ?, last_updated = ?, updated_at = ?
        WHERE address = ?`,
        [
          stats.totalTrades,
          stats.totalVolume,
          stats.totalPnl,
          stats.winRate,
          stats.sharpeRatio,
          stats.maxDrawdown,
          stats.avgPositionSize,
          Date.now(),
          Date.now(),
          address
        ]
      );
    } catch (error) {
      logger.error('Error updating trader stats:', error);
      throw error;
    }
  }

  async calculateTraderStats(address) {
    try {
      // 獲取最近30天的交易數據
      const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
      
      const trades = await database.all(
        'SELECT * FROM trades WHERE trader_address = ? AND timestamp >= ? ORDER BY timestamp',
        [address, thirtyDaysAgo]
      );

      if (trades.length === 0) {
        return {
          totalTrades: 0,
          totalVolume: 0,
          totalPnl: 0,
          winRate: 0,
          sharpeRatio: 0,
          maxDrawdown: 0,
          avgPositionSize: 0
        };
      }

      // 計算基本統計
      const totalTrades = trades.length;
      const totalVolume = trades.reduce((sum, trade) => sum + (trade.size * trade.price), 0);
      const totalPnl = trades.reduce((sum, trade) => sum + trade.pnl, 0);
      const avgPositionSize = totalVolume / totalTrades;

      // 計算勝率
      const winningTrades = trades.filter(trade => trade.pnl > 0).length;
      const winRate = winningTrades / totalTrades;

      // 計算夏普比率和最大回撤
      const returns = this.calculateReturns(trades);
      const sharpeRatio = this.calculateSharpeRatio(returns);
      const maxDrawdown = this.calculateMaxDrawdown(returns);

      return {
        totalTrades,
        totalVolume,
        totalPnl,
        winRate,
        sharpeRatio,
        maxDrawdown,
        avgPositionSize
      };
    } catch (error) {
      logger.error('Error calculating trader stats:', error);
      throw error;
    }
  }

  calculateReturns(trades) {
    const returns = [];
    let cumulativePnl = 0;
    
    for (const trade of trades) {
      cumulativePnl += trade.pnl;
      returns.push(trade.pnl);
    }
    
    return returns;
  }

  calculateSharpeRatio(returns) {
    if (returns.length < 2) return 0;
    
    const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / returns.length;
    const stdDev = Math.sqrt(variance);
    
    return stdDev === 0 ? 0 : mean / stdDev;
  }

  calculateMaxDrawdown(returns) {
    let maxDrawdown = 0;
    let peak = 0;
    let cumulative = 0;
    
    for (const ret of returns) {
      cumulative += ret;
      if (cumulative > peak) {
        peak = cumulative;
      }
      const drawdown = (peak - cumulative) / Math.abs(peak);
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown;
      }
    }
    
    return maxDrawdown;
  }

  async collectTraderData() {
    // 收集已追蹤交易員的最新數據
    for (const address of this.trackedTraders) {
      try {
        await this.updateTraderData(address);
      } catch (error) {
        logger.error(`Error updating trader data for ${address}:`, error);
      }
    }
  }

  async updateTraderData(address) {
    try {
      // 獲取最新的交易記錄
      const fills = await this.api.getUserFills(address);
      
      // 處理新的交易記錄
      for (const fill of fills) {
        await this.processTrade({ ...fill, user: address });
      }
      
      // 更新持倉信息
      await this.updateTraderPositions(address);
    } catch (error) {
      logger.error(`Error updating trader data for ${address}:`, error);
    }
  }

  async updateTraderPositions(address) {
    try {
      // 這裡需要實現獲取持倉信息的邏輯
      // Hyperliquid API 可能需要額外的端點來獲取持倉數據
      logger.debug(`Updating positions for trader: ${address}`);
    } catch (error) {
      logger.error(`Error updating positions for ${address}:`, error);
    }
  }

  async updatePositions() {
    // 更新所有追蹤交易員的持倉信息
    for (const address of this.trackedTraders) {
      try {
        await this.updateTraderPositions(address);
      } catch (error) {
        logger.error(`Error updating positions for ${address}:`, error);
      }
    }
  }

  async handleUserEvents(data) {
    // 處理用戶事件數據
    logger.debug('Received user events:', data);
  }

  getTrackedTradersCount() {
    return this.trackedTraders.size;
  }

  isTraderTracked(address) {
    return this.trackedTraders.has(address);
  }
}

export default DataCollector;
