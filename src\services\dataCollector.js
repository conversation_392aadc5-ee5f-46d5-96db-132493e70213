import EventEmitter from 'events';
import HyperliquidAPI from './hyperliquidApi.js';
import TraderEvaluator from './traderEvaluator.js';
import database from '../models/database.js';
import { config } from '../config/index.js';
import logger from '../utils/logger.js';

class DataCollector extends EventEmitter {
  constructor() {
    super();
    this.api = new HyperliquidAPI();
    this.evaluator = new TraderEvaluator();
    this.isRunning = false;
    
    // 改為追蹤頂尖交易員
    this.topTraders = new Set();
    this.lastFillTimestamps = new Map();

    this.collectionInterval = null;
    this.positionUpdateInterval = null;
    this.topTraderUpdateInterval = null;
    
    this.setupEventHandlers();
  }

  setupEventHandlers() {
    this.api.on('connected', () => {
      logger.info('Data collector connected to Hyperliquid API');
      this.subscribeToInitialData();
    });

    this.api.on('userEvents', (data) => {
      this.handleUserEvents(data);
    });

    this.api.on('error', (error) => {
      logger.error('Hyperliquid API error in DataCollector:', error);
    });
  }

  async start() {
    if (this.isRunning) {
      logger.warn('Data collector is already running');
      return;
    }
    logger.info('Starting data collector...');
    await database.initialize();
    this.api.connect();
    this.startPeriodicCollection();
    this.isRunning = true;
    logger.info('Data collector started successfully');
    this.emit('started');
  }

  async stop() {
    if (!this.isRunning) return;
    logger.info('Stopping data collector...');
    clearInterval(this.collectionInterval);
    clearInterval(this.positionUpdateInterval);
    clearInterval(this.topTraderUpdateInterval);
    this.api.disconnect();
    this.isRunning = false;
    logger.info('Data collector stopped');
    this.emit('stopped');
  }

  startPeriodicCollection() {
    // 1. 定期更新頂尖交易員列表
    this.topTraderUpdateInterval = setInterval(
      () => this.updateTopTradersList(),
      config.evaluation.updateIntervalMs
    );
    // 立即執行一次以獲取初始列表
    this.updateTopTradersList();

    // 2. 定期收集頂尖交易員的增量數據
    this.collectionInterval = setInterval(
      () => this.collectTopTraderData(),
      config.stats.updateIntervalMs
    );

    // 3. 定期更新持倉信息 (可選，優先使用WebSocket)
    this.positionUpdateInterval = setInterval(
      () => this.updateAllPositions(),
      config.stats.positionTrackingIntervalMs
    );
  }

  subscribeToInitialData() {
    // 訂閱市場數據，但不再從這裡發現交易員
    for (const coin of config.supportedCoins) {
      this.api.subscribeTrades(coin);
    }
    // WebSocket的用戶事件訂閱將在`updateTopTradersList`中動態處理
  }

  async updateTopTradersList() {
    logger.info('Updating top traders list...');
    try {
      const topTraderAddresses = await this.evaluator.getTopTraderAddresses(
        config.evaluation.topTradersCount
      );

      if (topTraderAddresses.length === 0) {
        logger.warn('No top traders found. Evaluation might need to run first.');
        return;
      }

      const newTopTraders = new Set(topTraderAddresses);
      const oldTopTraders = this.topTraders;

      // 計算需要新增訂閱和取消訂閱的地址
      const toUnsubscribe = [...oldTopTraders].filter(addr => !newTopTraders.has(addr));
      const toSubscribe = [...newTopTraders].filter(addr => !oldTopTraders.has(addr));

      // 動態更新WebSocket訂閱
      if (this.api.isConnected) {
        toUnsubscribe.forEach(address => this.api.unsubscribe({ type: 'userEvents', user: address }));
        toSubscribe.forEach(address => this.api.subscribe({ type: 'userEvents', user: address }));
      }

      this.topTraders = newTopTraders;
      logger.info(`Updated top traders list. Tracking ${this.topTraders.size} traders.`);
    } catch (error) {
      logger.error('Error updating top traders list:', error);
    }
  }

  async collectTopTraderData() {
    if (this.topTraders.size === 0) {
      logger.debug('No top traders to collect data for.');
      return;
    }
    
    logger.debug(`Collecting data for ${this.topTraders.size} top traders.`);
    for (const address of this.topTraders) {
      try {
        await this.updateTraderData(address);
      } catch (error) {
        logger.error(`Error in periodic data collection for ${address}:`, error);
      }
    }
  }

  async updateTraderData(address) {
    try {
      const lastTimestamp = this.lastFillTimestamps.get(address) || Date.now() - (30 * 24 * 60 * 60 * 1000); // 首次抓取最近30天
      
      // 使用 getUserFillsByTime 獲取增量數據
      const newFills = await this.api.getUserFillsByTime(address, lastTimestamp + 1);

      if (newFills && newFills.length > 0) {
        for (const fill of newFills) {
          // 處理和儲存單筆交易數據
          await this.processFill(address, fill);
        }
        // 更新時間戳
        const latestTimestamp = newFills[newFills.length - 1].time;
        this.lastFillTimestamps.set(address, latestTimestamp);
      }
      
      // 可選：更新一次持倉
      await this.updateTraderPositions(address);

    } catch (error) {
      logger.error(`Error updating trader data for ${address}:`, error);
    }
  }

  async processFill(userAddress, fill) {
    try {
      const uniqueHash = `${fill.hash}_${userAddress}_${fill.tid}`;
      
      await database.run(
        `INSERT OR IGNORE INTO trades (
          trader_address, coin, side, size, price, timestamp,
          hash, tid, pnl
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          userAddress,
          fill.coin,
          fill.side,
          parseFloat(fill.sz),
          parseFloat(fill.px),
          fill.time,
          uniqueHash,
          fill.tid,
          fill.pnl // 假設 pnl 字段存在
        ]
      );
      this.emit('tradeProcessed', { ...fill, trader_address: userAddress });
    } catch (error) {
      logger.error('Error storing fill:', error);
    }
  }

  async handleUserEvents(data) {
    if (!data || !data.user_address || !this.topTraders.has(data.user_address)) {
      return; // 只處理已追蹤的頂尖交易員事件
    }
    
    logger.debug(`Received user event for ${data.user_address}:`, data);

    try {
        // 根據事件類型處理
        if (data.event_type === 'fill') {
            await this.processFill(data.user_address, data.event_data);
            await this.updateTraderPositions(data.user_address);
        } else if (data.event_type === 'order') {
            // 可以增加訂單處理邏輯
        }
    } catch(error) {
        logger.error(`Error handling user event for ${data.user_address}:`, error);
    }
  }
  
  async updateAllPositions() {
    logger.debug(`Updating positions for ${this.topTraders.size} traders.`);
    for (const address of this.topTraders) {
      try {
        await this.updateTraderPositions(address);
      } catch (error) {
        logger.error(`Error updating positions for ${address}:`, error);
      }
    }
  }

  async updateTraderPositions(address) {
    try {
      const portfolioData = await this.api.getUserPortfolio(address);

      // API回傳的數據結構可能是一個數組，第一個元素是資產信息，第二個是持倉
      if (!portfolioData || !Array.isArray(portfolioData) || portfolioData.length < 2) {
        return;
      }
      
      const positions = portfolioData[1]?.positions;
      if (!positions || positions.length === 0) {
        // 如果沒有持倉，清空該用戶的持倉記錄
        await database.run('DELETE FROM positions WHERE trader_address = ?', [address]);
        return;
      }

      await database.transaction(async () => {
        // 先刪除該用戶所有舊的持倉記錄
        await database.run('DELETE FROM positions WHERE trader_address = ?', [address]);
        
        // 插入新的持倉記錄
        const now = Date.now();
        for (const pos of positions) {
          await database.run(
            `INSERT INTO positions (
                trader_address, coin, size, entry_price, unrealized_pnl, 
                timestamp, updated_at, created_at, is_open
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
              address,
              pos.coin,
              parseFloat(pos.szi),
              parseFloat(pos.entryPx),
              parseFloat(pos.unrealizedPnl),
              now,
              now,
              now,
              1 // 假設從API獲取的都是開倉中
            ]
          );
        }
      });
      
      this.emit('positionsUpdated', { address, positions });
    } catch (error) {
      logger.error(`Error updating positions for ${address}:`, error);
    }
  }

  getTrackedTradersCount() {
    return this.topTraders.size;
  }

  isTraderTracked(address) {
    return this.topTraders.has(address);
  }
}

export default DataCollector;
