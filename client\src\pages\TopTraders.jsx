import React, { useState, useEffect } from 'react'
import { Card, List, Avatar, Typography, Tag, Select, Spin, Alert, Button } from 'antd'
import { TrophyOutlined, RiseOutlined, FallOutlined, DollarOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { useApi } from '../hooks/useApi'
import { useSocket } from '../hooks/useSocket'

const { Title, Text } = Typography
const { Option } = Select

function TopTraders() {
  const [timeframe, setTimeframe] = useState('30d')
  const [traders, setTraders] = useState([])
  const navigate = useNavigate()
  
  const { data, loading, error, refetch } = useApi(`/api/traders/top?timeframe=${timeframe}&limit=50`)
  const socket = useSocket()

  useEffect(() => {
    if (data?.rankings) {
      setTraders(data.rankings)
    }
  }, [data])

  useEffect(() => {
    if (socket) {
      socket.on('topTradersUpdated', () => {
        refetch()
      })

      socket.on('initialData', (data) => {
        if (data.type === 'topTraders') {
          setTraders(data.data)
        }
      })

      return () => {
        socket.off('topTradersUpdated')
        socket.off('initialData')
      }
    }
  }, [socket, refetch])

  const getRankBadgeClass = (rank) => {
    if (rank === 1) return 'rank-1'
    if (rank === 2) return 'rank-2'
    if (rank === 3) return 'rank-3'
    return 'rank-other'
  }

  const formatAddress = (address) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`
  }

  const formatNumber = (num, decimals = 2) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(decimals)}M`
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(decimals)}K`
    }
    return num.toFixed(decimals)
  }

  const getScoreColor = (score) => {
    if (score >= 80) return '#52c41a'
    if (score >= 60) return '#faad14'
    if (score >= 40) return '#1890ff'
    return '#ff4d4f'
  }

  if (loading) {
    return (
      <div className="loading-container">
        <Spin size="large" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="error-container">
        <Alert
          message="載入失敗"
          description={error}
          type="error"
          showIcon
          action={
            <Button size="small" danger onClick={refetch}>
              重試
            </Button>
          }
        />
      </div>
    )
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2}>優秀交易員排行榜</Title>
        <Select
          value={timeframe}
          onChange={setTimeframe}
          style={{ width: 120 }}
        >
          <Option value="1h">1 小時</Option>
          <Option value="4h">4 小時</Option>
          <Option value="24h">24 小時</Option>
          <Option value="7d">7 天</Option>
          <Option value="30d">30 天</Option>
        </Select>
      </div>

      <Card>
        <List
          itemLayout="horizontal"
          dataSource={traders}
          renderItem={(trader) => (
            <List.Item
              className="trader-card"
              onClick={() => navigate(`/traders/${trader.trader_address}`)}
              actions={[
                <Button type="link" onClick={(e) => {
                  e.stopPropagation()
                  navigate(`/traders/${trader.trader_address}`)
                }}>
                  查看詳情
                </Button>
              ]}
            >
              <List.Item.Meta
                avatar={
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <div className={`rank-badge ${getRankBadgeClass(trader.rank_position)}`}>
                      {trader.rank_position}
                    </div>
                    <Avatar 
                      icon={<TrophyOutlined />} 
                      style={{ backgroundColor: getScoreColor(trader.score) }}
                    />
                  </div>
                }
                title={
                  <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                    <Text strong>{formatAddress(trader.trader_address)}</Text>
                    <Tag color={getScoreColor(trader.score)}>
                      評分: {trader.score}
                    </Tag>
                  </div>
                }
                description={
                  <div className="metric-item">
                    <div style={{ display: 'flex', gap: 16, flexWrap: 'wrap' }}>
                      <div className="metric-item">
                        <span className="metric-label">勝率:</span>
                        <span className={`metric-value ${trader.win_rate >= 0.6 ? 'positive' : trader.win_rate >= 0.4 ? 'neutral' : 'negative'}`}>
                          {(trader.win_rate * 100).toFixed(1)}%
                        </span>
                      </div>
                      
                      <div className="metric-item">
                        <span className="metric-label">總交易量:</span>
                        <span className="metric-value">
                          ${formatNumber(trader.total_volume)}
                        </span>
                      </div>
                      
                      <div className="metric-item">
                        <span className="metric-label">總盈虧:</span>
                        <span className={`metric-value ${trader.total_pnl >= 0 ? 'positive' : 'negative'}`}>
                          ${formatNumber(trader.total_pnl)}
                        </span>
                      </div>
                      
                      <div className="metric-item">
                        <span className="metric-label">交易次數:</span>
                        <span className="metric-value">
                          {trader.total_trades}
                        </span>
                      </div>
                    </div>
                  </div>
                }
              />
            </List.Item>
          )}
        />
      </Card>

      <div style={{ marginTop: 24 }}>
        <Alert
          message="排名說明"
          description={
            <div>
              <p>• 評分標準：綜合考慮勝率(25%)、盈利能力(30%)、夏普比率(20%)、最大回撤(15%)、一致性(10%)</p>
              <p>• 最低要求：勝率 ≥ 40%，盈利能力 ≥ 0，最大回撤 ≤ 50%，夏普比率 ≥ 0.5</p>
              <p>• 評估期間：{timeframe === '1h' ? '1小時' : timeframe === '4h' ? '4小時' : timeframe === '24h' ? '24小時' : timeframe === '7d' ? '7天' : '30天'}內的交易表現</p>
              <p>• 數據更新：每小時自動重新評估和排名</p>
            </div>
          }
          type="info"
          showIcon
        />
      </div>
    </div>
  )
}

export default TopTraders
