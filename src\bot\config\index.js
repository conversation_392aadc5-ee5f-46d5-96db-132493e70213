import dotenv from 'dotenv';

dotenv.config();

export const botConfig = {
  // Telegram Bot Token
  token: process.env.TELEGRAM_BOT_TOKEN,
  
  // Webhook 配置（可選，用於生產環境）
  webhook: {
    url: process.env.TELEGRAM_WEBHOOK_URL,
    port: parseInt(process.env.TELEGRAM_WEBHOOK_PORT) || 8443,
    enabled: process.env.TELEGRAM_USE_WEBHOOK === 'true'
  },
  
  // 速率限制配置
  rateLimit: {
    maxRequests: parseInt(process.env.BOT_RATE_LIMIT_MAX) || 30,
    windowMs: parseInt(process.env.BOT_RATE_LIMIT_WINDOW) || 60000, // 1分鐘
    skipSuccessfulRequests: true
  },
  
  // 功能開關
  features: {
    enableAlerts: process.env.BOT_ENABLE_ALERTS !== 'false',
    enableExport: process.env.BOT_ENABLE_EXPORT !== 'false',
    enableAdvancedStats: process.env.BOT_ENABLE_ADVANCED_STATS !== 'false',
    maxTopTraders: parseInt(process.env.BOT_MAX_TOP_TRADERS) || 50,
    maxSearchResults: parseInt(process.env.BOT_MAX_SEARCH_RESULTS) || 20
  },
  
  // 消息配置
  messages: {
    maxLength: 4096, // Telegram 消息最大長度
    parseMode: 'HTML', // 或 'Markdown'
    disableWebPagePreview: true
  },
  
  // 緩存配置
  cache: {
    userDataTtl: parseInt(process.env.BOT_USER_CACHE_TTL) || 3600, // 1小時
    statsDataTtl: parseInt(process.env.BOT_STATS_CACHE_TTL) || 300, // 5分鐘
    traderDataTtl: parseInt(process.env.BOT_TRADER_CACHE_TTL) || 600 // 10分鐘
  },
  
  // 分頁配置
  pagination: {
    defaultPageSize: parseInt(process.env.BOT_DEFAULT_PAGE_SIZE) || 10,
    maxPageSize: parseInt(process.env.BOT_MAX_PAGE_SIZE) || 50
  },
  
  // 語言配置
  languages: {
    default: process.env.BOT_DEFAULT_LANGUAGE || 'zh',
    supported: ['zh', 'en']
  },
  
  // 管理員配置
  admin: {
    userIds: process.env.BOT_ADMIN_USER_IDS 
      ? process.env.BOT_ADMIN_USER_IDS.split(',').map(id => parseInt(id.trim()))
      : [],
    enableAdminCommands: process.env.BOT_ENABLE_ADMIN_COMMANDS === 'true'
  },
  
  // 通知配置
  notifications: {
    enableBroadcast: process.env.BOT_ENABLE_BROADCAST === 'true',
    maxBroadcastUsers: parseInt(process.env.BOT_MAX_BROADCAST_USERS) || 1000,
    broadcastDelay: parseInt(process.env.BOT_BROADCAST_DELAY) || 100 // 毫秒
  }
};

// 指令配置
export const commandConfig = {
  // 基本指令
  basic: {
    start: {
      description: '開始使用機器人',
      usage: '/start',
      aliases: []
    },
    help: {
      description: '顯示幫助信息',
      usage: '/help [指令名稱]',
      aliases: ['h']
    },
    status: {
      description: '顯示系統狀態',
      usage: '/status',
      aliases: ['s']
    }
  },
  
  // 交易員查詢指令
  traders: {
    top: {
      description: '顯示頂級交易員排行榜',
      usage: '/top [數量] [時間範圍]',
      aliases: ['ranking', 'leaderboard'],
      examples: ['/top', '/top 20', '/top 10 24h']
    },
    trader: {
      description: '查詢特定交易員信息',
      usage: '/trader <地址>',
      aliases: ['t'],
      examples: ['/trader 0x1234...5678']
    },
    search: {
      description: '搜索交易員',
      usage: '/search <關鍵詞>',
      aliases: ['find'],
      examples: ['/search 0x1234']
    }
  },
  
  // 統計查詢指令
  stats: {
    stats: {
      description: '顯示交易統計',
      usage: '/stats [時間範圍]',
      aliases: ['statistics'],
      examples: ['/stats', '/stats 1h', '/stats 24h']
    },
    positions: {
      description: '顯示持倉統計',
      usage: '/positions [幣種]',
      aliases: ['pos'],
      examples: ['/positions', '/positions ETH']
    },
    volume: {
      description: '顯示交易量統計',
      usage: '/volume [時間範圍]',
      aliases: ['vol'],
      examples: ['/volume', '/volume 24h']
    },
    performance: {
      description: '顯示績效統計',
      usage: '/performance [時間範圍]',
      aliases: ['perf'],
      examples: ['/performance', '/performance 7d']
    }
  },
  
  // 通知管理指令
  alerts: {
    alerts: {
      description: '管理通知設置',
      usage: '/alerts',
      aliases: ['notifications']
    },
    subscribe: {
      description: '訂閱通知',
      usage: '/subscribe <類型>',
      aliases: ['sub'],
      examples: ['/subscribe top_traders', '/subscribe volume_alerts']
    },
    unsubscribe: {
      description: '取消訂閱',
      usage: '/unsubscribe <類型>',
      aliases: ['unsub'],
      examples: ['/unsubscribe top_traders']
    }
  }
};

// 消息模板
export const messageTemplates = {
  welcome: {
    zh: `🎉 歡迎使用 Hyperliquid 交易員追蹤機器人！

🚀 這個機器人可以幫助您：
• 📊 查看頂級交易員排行榜
• 📈 獲取實時交易統計
• 🔍 搜索和分析特定交易員
• 🔔 設置自定義通知

💡 使用 /help 查看所有可用指令
🌟 使用 /top 查看當前頂級交易員`,
    
    en: `🎉 Welcome to Hyperliquid Trader Tracker Bot!

🚀 This bot can help you:
• 📊 View top trader leaderboards
• 📈 Get real-time trading statistics
• 🔍 Search and analyze specific traders
• 🔔 Set up custom notifications

💡 Use /help to see all available commands
🌟 Use /top to view current top traders`
  },
  
  help: {
    zh: `📚 可用指令列表：

🔰 基本指令：
/start - 開始使用
/help - 顯示此幫助
/status - 系統狀態

👥 交易員查詢：
/top [數量] - 頂級交易員排行榜
/trader <地址> - 查詢特定交易員
/search <關鍵詞> - 搜索交易員

📊 統計查詢：
/stats [時間] - 交易統計
/positions [幣種] - 持倉統計
/volume [時間] - 交易量統計
/performance [時間] - 績效統計

🔔 通知管理：
/alerts - 通知設置
/subscribe <類型> - 訂閱通知
/unsubscribe <類型> - 取消訂閱

💡 提示：點擊指令可直接使用`,
    
    en: `📚 Available Commands:

🔰 Basic Commands:
/start - Start using the bot
/help - Show this help
/status - System status

👥 Trader Queries:
/top [count] - Top trader leaderboard
/trader <address> - Query specific trader
/search <keyword> - Search traders

📊 Statistics:
/stats [timeframe] - Trading statistics
/positions [coin] - Position statistics
/volume [timeframe] - Volume statistics
/performance [timeframe] - Performance statistics

🔔 Notifications:
/alerts - Notification settings
/subscribe <type> - Subscribe to notifications
/unsubscribe <type> - Unsubscribe from notifications

💡 Tip: Click on commands to use them directly`
  },
  
  error: {
    zh: '❌ 發生錯誤，請稍後重試。',
    en: '❌ An error occurred, please try again later.'
  },
  
  notFound: {
    zh: '❌ 未找到相關數據。',
    en: '❌ No data found.'
  },
  
  invalidInput: {
    zh: '❌ 輸入格式不正確，請檢查後重試。',
    en: '❌ Invalid input format, please check and try again.'
  }
};

// 時間範圍配置
export const timeframes = {
  '1h': { label: '1小時', seconds: 3600 },
  '4h': { label: '4小時', seconds: 14400 },
  '24h': { label: '24小時', seconds: 86400 },
  '7d': { label: '7天', seconds: 604800 },
  '30d': { label: '30天', seconds: 2592000 }
};

// 訂閱類型配置
export const subscriptionTypes = {
  top_traders: {
    name: '頂級交易員更新',
    description: '當頂級交易員排行榜發生變化時通知'
  },
  volume_alerts: {
    name: '交易量警報',
    description: '當交易量達到設定閾值時通知'
  },
  performance_alerts: {
    name: '績效警報',
    description: '當交易員績效發生重大變化時通知'
  },
  new_traders: {
    name: '新交易員發現',
    description: '當發現新的優秀交易員時通知'
  }
};
