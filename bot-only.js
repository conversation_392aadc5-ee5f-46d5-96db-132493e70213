import dotenv from 'dotenv';
import logger from './src/utils/logger.js';
import database from './src/models/database.js';
import cacheService from './src/services/cacheService.js';
import HyperliquidBot from './src/bot/index.js';

dotenv.config();

class BotOnlyApp {
  constructor() {
    this.telegramBot = null;
    this.isShuttingDown = false;
  }

  async initialize() {
    try {
      logger.info('Initializing Telegram Bot Only...');

      // 初始化數據庫
      await database.initialize();
      logger.info('Database initialized');

      // 初始化緩存
      await cacheService.initialize();
      logger.info('Cache service initialized');

      // 創建並啟動 Telegram Bot
      this.telegramBot = new HyperliquidBot();
      await this.telegramBot.initialize();
      logger.info('Telegram bot started successfully');

      logger.info('Bot-only application initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize bot-only application:', error);
      throw error;
    }
  }

  async start() {
    try {
      await this.initialize();

      logger.info('Telegram bot is running...');
      logger.info('Press Ctrl+C to stop');

      // 優雅關閉處理
      this.setupGracefulShutdown();

    } catch (error) {
      logger.error('Failed to start bot:', error);
      process.exit(1);
    }
  }

  setupGracefulShutdown() {
    const shutdown = async (signal) => {
      if (this.isShuttingDown) return;
      this.isShuttingDown = true;

      logger.info(`Received ${signal}, starting graceful shutdown...`);

      try {
        // 停止 Telegram Bot
        if (this.telegramBot) {
          await this.telegramBot.stop();
        }

        // 關閉數據庫連接
        await database.close();

        // 關閉緩存連接
        await cacheService.disconnect();

        logger.info('Graceful shutdown completed');
        process.exit(0);
      } catch (error) {
        logger.error('Error during shutdown:', error);
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));

    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception:', error);
      shutdown('uncaughtException');
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
      shutdown('unhandledRejection');
    });
  }
}

// 啟動應用
const app = new BotOnlyApp();
app.start().catch(error => {
  logger.error('Failed to start bot application:', error);
  process.exit(1);
});

export default app;
