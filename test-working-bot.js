import TelegramBot from 'node-telegram-bot-api';
import dotenv from 'dotenv';

dotenv.config();

async function testWorkingBot() {
  try {
    console.log('🧪 測試工作版本機器人...');
    
    const bot = new TelegramBot(process.env.TELEGRAM_BOT_TOKEN, { polling: false });
    const chatId = 251366744; // <PERSON>'s chat ID
    
    console.log('📤 發送 /help 命令...');
    await bot.sendMessage(chatId, '/help');
    
    console.log('⏳ 等待 3 秒...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('📤 發送 /top 命令...');
    await bot.sendMessage(chatId, '/top');
    
    console.log('⏳ 等待 3 秒...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('📤 發送 /top 5 命令...');
    await bot.sendMessage(chatId, '/top 5');
    
    console.log('⏳ 等待 3 秒...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('📤 發送 /status 命令...');
    await bot.sendMessage(chatId, '/status');
    
    console.log('⏳ 等待 3 秒...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('📤 發送測試訊息...');
    await bot.sendMessage(chatId, '測試機器人功能');
    
    console.log('✅ 所有測試命令已發送！');
    console.log('🔍 請檢查 Telegram 和機器人日誌');
    
  } catch (error) {
    console.error('❌ 測試失敗:', error.message);
  }
}

testWorkingBot();
