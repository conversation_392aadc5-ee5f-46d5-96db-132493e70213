module.exports = {
  apps: [
    {
      name: 'hyperliquid-tracker',
      script: 'src/index.js',
      instances: 1,
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'development',
        PORT: 3000
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      // 日誌配置
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // 重啟配置
      max_restarts: 10,
      min_uptime: '10s',
      max_memory_restart: '1G',
      
      // 監控配置
      watch: false,
      ignore_watch: ['node_modules', 'logs', 'data'],
      
      // 其他配置
      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 10000
    }
  ],

  deploy: {
    production: {
      user: 'deploy',
      host: 'your-server.com',
      ref: 'origin/main',
      repo: '**************:your-username/hyperliquid-tracker.git',
      path: '/var/www/hyperliquid-tracker',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run build:client && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
}
