import database from './src/models/database.js';

async function fixTraderData() {
  try {
    console.log('🔧 修復交易員數據...');
    
    await database.initialize();
    
    // 獲取所有有交易記錄的交易員
    const traders = await database.all(`
      SELECT DISTINCT trader_address as address
      FROM trades 
      WHERE trader_address IS NOT NULL
    `);
    
    console.log(`找到 ${traders.length} 名有交易記錄的交易員`);
    
    for (let i = 0; i < traders.length; i++) {
      const trader = traders[i];
      console.log(`\n處理交易員 ${i + 1}/${traders.length}: ${trader.address}`);
      
      try {
        // 獲取該交易員的所有交易
        const trades = await database.all(`
          SELECT 
            side,
            size,
            price,
            timestamp,
            pnl
          FROM trades 
          WHERE trader_address = ?
          ORDER BY timestamp ASC
        `, [trader.address]);
        
        console.log(`  找到 ${trades.length} 筆交易`);
        
        if (trades.length === 0) continue;
        
        // 計算統計數據
        let totalVolume = 0;
        let totalPnl = 0;
        let winningTrades = 0;
        let position = 0; // 當前持倉
        let realizedPnl = 0;
        let entryPrice = 0;
        let entrySize = 0;
        
        for (const trade of trades) {
          const size = parseFloat(trade.size);
          const price = parseFloat(trade.price);
          const volume = size * price;
          totalVolume += volume;
          
          // 簡化的 PnL 計算邏輯
          if (trade.side === 'B') { // Buy/Long
            if (position <= 0) {
              // 開多倉或平空倉
              if (position < 0) {
                // 平空倉，計算 PnL
                const closedSize = Math.min(size, Math.abs(position));
                const pnl = closedSize * (entryPrice - price);
                realizedPnl += pnl;
                if (pnl > 0) winningTrades++;
                
                position += closedSize;
                if (position === 0) {
                  entryPrice = 0;
                  entrySize = 0;
                }
              }
              
              if (size > Math.abs(Math.min(position, 0))) {
                // 開新多倉
                const newSize = size - Math.abs(Math.min(position, 0));
                entryPrice = price;
                entrySize = newSize;
                position = Math.max(position, 0) + newSize;
              }
            } else {
              // 加多倉
              const avgPrice = (entryPrice * entrySize + price * size) / (entrySize + size);
              entryPrice = avgPrice;
              entrySize += size;
              position += size;
            }
          } else { // Sell/Short
            if (position >= 0) {
              // 平多倉或開空倉
              if (position > 0) {
                // 平多倉，計算 PnL
                const closedSize = Math.min(size, position);
                const pnl = closedSize * (price - entryPrice);
                realizedPnl += pnl;
                if (pnl > 0) winningTrades++;
                
                position -= closedSize;
                if (position === 0) {
                  entryPrice = 0;
                  entrySize = 0;
                }
              }
              
              if (size > Math.max(position, 0)) {
                // 開新空倉
                const newSize = size - Math.max(position, 0);
                entryPrice = price;
                entrySize = newSize;
                position = Math.min(position, 0) - newSize;
              }
            } else {
              // 加空倉
              const avgPrice = (entryPrice * Math.abs(entrySize) + price * size) / (Math.abs(entrySize) + size);
              entryPrice = avgPrice;
              entrySize += size;
              position -= size;
            }
          }
        }
        
        totalPnl = realizedPnl;
        
        // 計算勝率
        const totalClosingTrades = winningTrades + (trades.length - winningTrades);
        const winRate = totalClosingTrades > 0 ? winningTrades / totalClosingTrades : 0;
        
        // 計算簡化的績效分數
        const performanceScore = winRate * 0.4 + Math.max(0, totalPnl / 1000) * 0.6;
        
        console.log(`  統計結果:`);
        console.log(`    總交易數: ${trades.length}`);
        console.log(`    總交易量: $${totalVolume.toFixed(2)}`);
        console.log(`    總 PnL: $${totalPnl.toFixed(2)}`);
        console.log(`    勝率: ${(winRate * 100).toFixed(1)}%`);
        console.log(`    績效分數: ${performanceScore.toFixed(2)}`);
        
        // 更新數據庫
        await database.run(`
          UPDATE traders 
          SET 
            total_trades = ?,
            total_volume = ?,
            total_pnl = ?,
            win_rate = ?,
            performance_score = ?,
            last_updated = ?,
            updated_at = ?
          WHERE address = ?
        `, [
          trades.length,
          totalVolume,
          totalPnl,
          winRate,
          performanceScore,
          Date.now(),
          Date.now(),
          trader.address
        ]);
        
        console.log(`  ✅ 已更新交易員數據`);
        
      } catch (error) {
        console.error(`  ❌ 處理交易員 ${trader.address} 時出錯:`, error.message);
      }
    }
    
    console.log('\n🎉 數據修復完成！');
    
    // 顯示修復後的統計
    const stats = await database.get(`
      SELECT 
        COUNT(*) as total_traders,
        COUNT(CASE WHEN total_pnl > 0 THEN 1 END) as profitable_traders,
        AVG(win_rate) as avg_win_rate,
        SUM(total_pnl) as total_pnl,
        SUM(total_volume) as total_volume
      FROM traders 
      WHERE is_active = 1 AND total_trades > 0
    `);
    
    console.log('\n📊 修復後統計:');
    console.log(`  總交易員: ${stats.total_traders}`);
    console.log(`  盈利交易員: ${stats.profitable_traders}`);
    console.log(`  平均勝率: ${((stats.avg_win_rate || 0) * 100).toFixed(1)}%`);
    console.log(`  總 PnL: $${(stats.total_pnl || 0).toFixed(2)}`);
    console.log(`  總交易量: $${((stats.total_volume || 0) / 1000000).toFixed(2)}M`);
    
    // 顯示前5名交易員
    console.log('\n🏆 前5名交易員:');
    const topTraders = await database.all(`
      SELECT 
        address,
        total_trades,
        win_rate,
        total_pnl,
        total_volume
      FROM traders 
      WHERE is_active = 1 AND total_trades > 0
      ORDER BY total_pnl DESC 
      LIMIT 5
    `);
    
    topTraders.forEach((trader, index) => {
      const address = trader.address.slice(0, 6) + '...' + trader.address.slice(-4);
      console.log(`  ${index + 1}. ${address}`);
      console.log(`     PnL: $${trader.total_pnl.toFixed(2)}`);
      console.log(`     勝率: ${(trader.win_rate * 100).toFixed(1)}%`);
      console.log(`     交易數: ${trader.total_trades}`);
      console.log(`     交易量: $${(trader.total_volume / 1000).toFixed(1)}K`);
    });
    
  } catch (error) {
    console.error('❌ 修復失敗:', error.message);
    console.error(error.stack);
  }
}

fixTraderData();
