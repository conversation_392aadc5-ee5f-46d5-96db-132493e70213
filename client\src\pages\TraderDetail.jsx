import React, { useState, useEffect } from 'react'
import { usePara<PERSON>, useNavigate } from 'react-router-dom'
import { Card, Row, Col, Typography, Statistic, Table, Tag, Button, Spin, Alert, Tabs } from 'antd'
import { ArrowLeftOutlined, TrophyOutlined, RiseOutlined, FallOutlined } from '@ant-design/icons'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'
import { useApi } from '../hooks/useApi'

const { Title, Text } = Typography
const { TabPane } = Tabs

function TraderDetail() {
  const { address } = useParams()
  const navigate = useNavigate()
  const [timeframe, setTimeframe] = useState('30d')
  
  const { data: traderData, loading, error } = useApi(`/api/traders/${address}?timeframe=${timeframe}`)

  const formatAddress = (addr) => {
    return `${addr.slice(0, 8)}...${addr.slice(-6)}`
  }

  const formatNumber = (num, decimals = 2) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(decimals)}M`
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(decimals)}K`
    }
    return num.toFixed(decimals)
  }

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleString('zh-TW')
  }

  const getScoreColor = (score) => {
    if (score >= 80) return '#52c41a'
    if (score >= 60) return '#faad14'
    if (score >= 40) return '#1890ff'
    return '#ff4d4f'
  }

  if (loading) {
    return (
      <div className="loading-container">
        <Spin size="large" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="error-container">
        <Alert
          message="載入失敗"
          description={error}
          type="error"
          showIcon
          action={
            <Button onClick={() => navigate('/traders')}>
              返回列表
            </Button>
          }
        />
      </div>
    )
  }

  const trader = traderData?.trader
  const evaluation = traderData?.evaluation
  const recentTrades = traderData?.recentTrades || []

  // 準備交易歷史圖表數據
  const chartData = recentTrades.slice(-20).map((trade, index) => ({
    index: index + 1,
    pnl: trade.pnl,
    cumulativePnl: recentTrades.slice(0, recentTrades.indexOf(trade) + 1)
      .reduce((sum, t) => sum + t.pnl, 0)
  }))

  // 交易表格列定義
  const tradeColumns = [
    {
      title: '時間',
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (timestamp) => formatTime(timestamp),
      width: 150
    },
    {
      title: '幣種',
      dataIndex: 'coin',
      key: 'coin',
      width: 80
    },
    {
      title: '方向',
      dataIndex: 'side',
      key: 'side',
      render: (side) => (
        <Tag color={side === 'B' ? 'green' : 'red'}>
          {side === 'B' ? '做多' : '做空'}
        </Tag>
      ),
      width: 80
    },
    {
      title: '數量',
      dataIndex: 'size',
      key: 'size',
      render: (size) => formatNumber(size, 4),
      width: 100
    },
    {
      title: '價格',
      dataIndex: 'price',
      key: 'price',
      render: (price) => `$${formatNumber(price)}`,
      width: 100
    },
    {
      title: '盈虧',
      dataIndex: 'pnl',
      key: 'pnl',
      render: (pnl) => (
        <Text className={pnl >= 0 ? 'positive' : 'negative'}>
          ${formatNumber(pnl)}
        </Text>
      ),
      width: 100
    }
  ]

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Button 
          icon={<ArrowLeftOutlined />} 
          onClick={() => navigate('/traders')}
          style={{ marginRight: 16 }}
        >
          返回列表
        </Button>
        <Title level={2} style={{ display: 'inline' }}>
          交易員詳情: {formatAddress(address)}
        </Title>
      </div>

      {/* 基本信息 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="綜合評分"
              value={evaluation?.score || 0}
              precision={1}
              valueStyle={{ color: getScoreColor(evaluation?.score || 0) }}
              prefix={<TrophyOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="勝率"
              value={(evaluation?.winRate || 0) * 100}
              precision={1}
              suffix="%"
              valueStyle={{ color: evaluation?.winRate >= 0.6 ? '#3f8600' : '#cf1322' }}
              prefix={<RiseOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="總盈虧"
              value={trader?.total_pnl || 0}
              precision={2}
              prefix="$"
              valueStyle={{ color: (trader?.total_pnl || 0) >= 0 ? '#3f8600' : '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="總交易量"
              value={trader?.total_volume || 0}
              precision={0}
              prefix="$"
              formatter={(value) => formatNumber(value)}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={16}>
        {/* 詳細指標 */}
        <Col span={8}>
          <Card title="詳細指標" style={{ height: 400 }}>
            {evaluation && (
              <div>
                <div className="metric-item">
                  <span className="metric-label">夏普比率:</span>
                  <span className="metric-value">{evaluation.sharpeRatio?.toFixed(2) || 'N/A'}</span>
                </div>
                
                <div className="metric-item">
                  <span className="metric-label">最大回撤:</span>
                  <span className="metric-value negative">
                    {((evaluation.maxDrawdown || 0) * 100).toFixed(1)}%
                  </span>
                </div>
                
                <div className="metric-item">
                  <span className="metric-label">盈利能力:</span>
                  <span className={`metric-value ${evaluation.profitability >= 0 ? 'positive' : 'negative'}`}>
                    {((evaluation.profitability || 0) * 100).toFixed(2)}%
                  </span>
                </div>
                
                <div className="metric-item">
                  <span className="metric-label">一致性:</span>
                  <span className="metric-value">{evaluation.consistency?.toFixed(2) || 'N/A'}</span>
                </div>
                
                <div className="metric-item">
                  <span className="metric-label">平均交易規模:</span>
                  <span className="metric-value">${formatNumber(evaluation.avgTradeSize || 0)}</span>
                </div>
                
                <div className="metric-item">
                  <span className="metric-label">交易頻率:</span>
                  <span className="metric-value">
                    {(evaluation.tradingFrequency || 0).toFixed(1)} 次/天
                  </span>
                </div>
                
                <div className="metric-item">
                  <span className="metric-label">總交易次數:</span>
                  <span className="metric-value">{trader?.total_trades || 0}</span>
                </div>
                
                <div className="metric-item">
                  <span className="metric-label">評估期間:</span>
                  <span className="metric-value">
                    {evaluation.evaluationPeriod?.tradeCount || 0} 筆交易
                  </span>
                </div>
              </div>
            )}
          </Card>
        </Col>

        {/* 盈虧趨勢圖 */}
        <Col span={16}>
          <Card title="最近交易盈虧趨勢" style={{ height: 400 }}>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="index" />
                <YAxis />
                <Tooltip 
                  formatter={(value, name) => [
                    `$${formatNumber(value)}`,
                    name === 'pnl' ? '單筆盈虧' : '累計盈虧'
                  ]}
                />
                <Line 
                  type="monotone" 
                  dataKey="cumulativePnl" 
                  stroke="#1890ff" 
                  strokeWidth={2}
                  name="累計盈虧"
                />
                <Line 
                  type="monotone" 
                  dataKey="pnl" 
                  stroke="#52c41a" 
                  strokeWidth={1}
                  name="單筆盈虧"
                />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      {/* 交易歷史 */}
      <Row style={{ marginTop: 24 }}>
        <Col span={24}>
          <Card title="最近交易記錄">
            <Table
              columns={tradeColumns}
              dataSource={recentTrades}
              rowKey={(record) => `${record.timestamp}-${record.coin}-${record.side}`}
              pagination={{
                pageSize: 20,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 筆交易`
              }}
              scroll={{ x: 800 }}
            />
          </Card>
        </Col>
      </Row>

      <div style={{ marginTop: 24 }}>
        <Alert
          message="數據說明"
          description={
            <div>
              <p>• 評估期間：{timeframe === '30d' ? '過去 30 天' : timeframe === '7d' ? '過去 7 天' : '過去 24 小時'}內的交易表現</p>
              <p>• 綜合評分：基於勝率、盈利能力、風險控制等多項指標計算</p>
              <p>• 夏普比率：衡量風險調整後的收益表現</p>
              <p>• 最大回撤：投資組合從峰值到谷值的最大跌幅</p>
            </div>
          }
          type="info"
          showIcon
        />
      </div>
    </div>
  )
}

export default TraderDetail
