import EventEmitter from 'events';
import database from '../models/database.js';
import TraderEvaluator from './traderEvaluator.js';
import { config } from '../config/index.js';
import logger from '../utils/logger.js';

class RealTimeStats extends EventEmitter {
  constructor() {
    super();
    this.traderEvaluator = new TraderEvaluator();
    this.topTraders = new Map(); // 緩存優秀交易員列表
    this.positionStats = new Map(); // 實時持倉統計
    this.updateInterval = null;
    this.isRunning = false;
    
    // 統計時間框架
    this.timeframes = config.stats.timeframes;
    
    // 初始化統計數據結構
    this.initializeStats();
  }

  initializeStats() {
    for (const timeframe of this.timeframes) {
      this.positionStats.set(timeframe, {
        totalTraders: 0,
        activeTraders: 0,
        positions: new Map(), // coin -> { long: count, short: count }
        volume: 0,
        lastUpdate: Date.now()
      });
    }
  }

  async start() {
    if (this.isRunning) {
      logger.warn('Real-time stats service is already running');
      return;
    }

    try {
      logger.info('Starting real-time stats service...');
      
      // 初始化優秀交易員列表
      await this.updateTopTraders();
      
      // 開始定期更新
      this.startPeriodicUpdates();
      
      this.isRunning = true;
      logger.info('Real-time stats service started successfully');
      
      this.emit('started');
    } catch (error) {
      logger.error('Error starting real-time stats service:', error);
      throw error;
    }
  }

  async stop() {
    if (!this.isRunning) return;

    logger.info('Stopping real-time stats service...');
    
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    
    this.isRunning = false;
    logger.info('Real-time stats service stopped');
    
    this.emit('stopped');
  }

  startPeriodicUpdates() {
    // 定期更新統計數據
    this.updateInterval = setInterval(async () => {
      try {
        await this.updateStats();
      } catch (error) {
        logger.error('Error in periodic stats update:', error);
      }
    }, config.stats.updateIntervalMs);

    // 每小時更新一次優秀交易員列表
    setInterval(async () => {
      try {
        await this.updateTopTraders();
      } catch (error) {
        logger.error('Error updating top traders:', error);
      }
    }, 60 * 60 * 1000); // 1小時
  }

  // 更新優秀交易員列表
  async updateTopTraders() {
    try {
      logger.info('Updating top traders list...');
      
      // 獲取各個時間框架的優秀交易員
      for (const timeframe of this.timeframes) {
        const rankings = await this.traderEvaluator.getRankings(timeframe, 100);
        this.topTraders.set(timeframe, new Set(rankings.map(r => r.trader_address)));
      }
      
      logger.info('Top traders list updated successfully');
      this.emit('topTradersUpdated');
    } catch (error) {
      logger.error('Error updating top traders:', error);
      throw error;
    }
  }

  // 更新統計數據
  async updateStats() {
    try {
      for (const timeframe of this.timeframes) {
        await this.updateTimeframeStats(timeframe);
      }
      
      this.emit('statsUpdated');
    } catch (error) {
      logger.error('Error updating stats:', error);
      throw error;
    }
  }

  // 更新特定時間框架的統計數據
  async updateTimeframeStats(timeframe) {
    try {
      const timeframeDays = this.parseTimeframe(timeframe);
      const startTime = Date.now() - (timeframeDays * 24 * 60 * 60 * 1000);
      
      // 獲取該時間框架內的優秀交易員
      const topTraders = this.topTraders.get(timeframe) || new Set();
      
      // 計算各幣種的開單統計
      for (const coin of config.supportedCoins) {
        const stats = await this.calculateCoinStats(coin, topTraders, startTime);
        await this.saveCoinStats(timeframe, coin, stats);
      }
      
      // 計算總體統計
      const overallStats = await this.calculateOverallStats(topTraders, startTime);
      await this.saveCoinStats(timeframe, null, overallStats);
      
    } catch (error) {
      logger.error(`Error updating stats for timeframe ${timeframe}:`, error);
      throw error;
    }
  }

  // 計算特定幣種的統計數據
  async calculateCoinStats(coin, topTraders, startTime) {
    try {
      const query = `
        SELECT 
          trader_address,
          side,
          COUNT(*) as trade_count,
          SUM(size) as total_size,
          AVG(size) as avg_size
        FROM trades 
        WHERE coin = ? AND timestamp >= ?
        GROUP BY trader_address, side
      `;
      
      const trades = await database.all(query, [coin, startTime]);
      
      let longPositions = 0;
      let shortPositions = 0;
      let totalVolume = 0;
      let activeTraders = new Set();
      
      for (const trade of trades) {
        if (topTraders.has(trade.trader_address)) {
          activeTraders.add(trade.trader_address);
          totalVolume += trade.total_size;
          
          if (trade.side === 'B') { // Buy = Long
            longPositions += trade.trade_count;
          } else { // Sell = Short
            shortPositions += trade.trade_count;
          }
        }
      }
      
      const totalPositions = longPositions + shortPositions;
      const longPercentage = totalPositions > 0 ? (longPositions / totalPositions) * 100 : 0;
      const shortPercentage = totalPositions > 0 ? (shortPositions / totalPositions) * 100 : 0;
      
      return {
        totalTraders: topTraders.size,
        activeTraders: activeTraders.size,
        longPositions,
        shortPositions,
        longPercentage,
        shortPercentage,
        avgPositionSize: activeTraders.size > 0 ? totalVolume / activeTraders.size : 0,
        totalVolume
      };
    } catch (error) {
      logger.error(`Error calculating coin stats for ${coin}:`, error);
      throw error;
    }
  }

  // 計算總體統計數據
  async calculateOverallStats(topTraders, startTime) {
    try {
      const query = `
        SELECT 
          trader_address,
          side,
          COUNT(*) as trade_count,
          SUM(size * price) as volume
        FROM trades 
        WHERE timestamp >= ?
        GROUP BY trader_address, side
      `;
      
      const trades = await database.all(query, [startTime]);
      
      let longPositions = 0;
      let shortPositions = 0;
      let totalVolume = 0;
      let activeTraders = new Set();
      
      for (const trade of trades) {
        if (topTraders.has(trade.trader_address)) {
          activeTraders.add(trade.trader_address);
          totalVolume += trade.volume;
          
          if (trade.side === 'B') {
            longPositions += trade.trade_count;
          } else {
            shortPositions += trade.trade_count;
          }
        }
      }
      
      const totalPositions = longPositions + shortPositions;
      const longPercentage = totalPositions > 0 ? (longPositions / totalPositions) * 100 : 0;
      const shortPercentage = totalPositions > 0 ? (shortPositions / totalPositions) * 100 : 0;
      
      return {
        totalTraders: topTraders.size,
        activeTraders: activeTraders.size,
        longPositions,
        shortPositions,
        longPercentage,
        shortPercentage,
        avgPositionSize: activeTraders.size > 0 ? totalVolume / activeTraders.size : 0,
        totalVolume
      };
    } catch (error) {
      logger.error('Error calculating overall stats:', error);
      throw error;
    }
  }

  // 保存統計數據
  async saveCoinStats(timeframe, coin, stats) {
    try {
      const timestamp = Date.now();
      
      await database.run(
        `INSERT OR REPLACE INTO real_time_stats (
          timeframe, coin, total_traders, active_traders,
          long_positions, short_positions, long_percentage, short_percentage,
          avg_position_size, total_volume, timestamp
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          timeframe,
          coin,
          stats.totalTraders,
          stats.activeTraders,
          stats.longPositions,
          stats.shortPositions,
          stats.longPercentage,
          stats.shortPercentage,
          stats.avgPositionSize,
          stats.totalVolume,
          timestamp
        ]
      );
    } catch (error) {
      logger.error('Error saving coin stats:', error);
      throw error;
    }
  }

  // 獲取實時統計數據
  async getStats(timeframe = '1h', coin = null) {
    try {
      let query = `
        SELECT * FROM real_time_stats 
        WHERE timeframe = ?
      `;
      const params = [timeframe];
      
      if (coin) {
        query += ' AND coin = ?';
        params.push(coin);
      } else {
        query += ' AND coin IS NULL';
      }
      
      query += ' ORDER BY timestamp DESC LIMIT 1';
      
      return await database.get(query, params);
    } catch (error) {
      logger.error('Error getting stats:', error);
      throw error;
    }
  }

  // 獲取所有幣種的統計數據
  async getAllCoinStats(timeframe = '1h') {
    try {
      const query = `
        SELECT * FROM real_time_stats 
        WHERE timeframe = ? AND coin IS NOT NULL
        AND timestamp = (
          SELECT MAX(timestamp) FROM real_time_stats 
          WHERE timeframe = ? AND coin IS NOT NULL
        )
        ORDER BY coin
      `;
      
      return await database.all(query, [timeframe, timeframe]);
    } catch (error) {
      logger.error('Error getting all coin stats:', error);
      throw error;
    }
  }

  // 獲取歷史統計數據
  async getHistoricalStats(timeframe = '1h', coin = null, hours = 24) {
    try {
      const startTime = Date.now() - (hours * 60 * 60 * 1000);
      
      let query = `
        SELECT * FROM real_time_stats 
        WHERE timeframe = ? AND timestamp >= ?
      `;
      const params = [timeframe, startTime];
      
      if (coin) {
        query += ' AND coin = ?';
        params.push(coin);
      } else {
        query += ' AND coin IS NULL';
      }
      
      query += ' ORDER BY timestamp ASC';
      
      return await database.all(query, params);
    } catch (error) {
      logger.error('Error getting historical stats:', error);
      throw error;
    }
  }

  // 處理新交易事件
  async handleNewTrade(trade) {
    try {
      // 檢查是否是優秀交易員的交易
      for (const [timeframe, traders] of this.topTraders) {
        if (traders.has(trade.trader_address)) {
          // 實時更新統計
          await this.updateRealTimeStats(timeframe, trade);
          
          // 發出事件
          this.emit('tradeFromTopTrader', {
            timeframe,
            trade,
            traderRank: await this.getTraderRank(trade.trader_address, timeframe)
          });
        }
      }
    } catch (error) {
      logger.error('Error handling new trade:', error);
    }
  }

  // 實時更新統計數據
  async updateRealTimeStats(timeframe, trade) {
    try {
      // 這裡可以實現更細粒度的實時更新邏輯
      // 例如立即更新緩存中的統計數據
      logger.debug(`Real-time update for ${timeframe}: ${trade.coin} ${trade.side}`);
    } catch (error) {
      logger.error('Error updating real-time stats:', error);
    }
  }

  // 獲取交易員排名
  async getTraderRank(address, timeframe) {
    try {
      const query = `
        SELECT rank_position FROM trader_rankings 
        WHERE trader_address = ? AND timeframe = ?
        ORDER BY timestamp DESC LIMIT 1
      `;
      
      const result = await database.get(query, [address, timeframe]);
      return result ? result.rank_position : null;
    } catch (error) {
      logger.error('Error getting trader rank:', error);
      return null;
    }
  }

  // 生成統計報告
  async generateReport(timeframe = '1h') {
    try {
      const overallStats = await this.getStats(timeframe);
      const coinStats = await this.getAllCoinStats(timeframe);
      
      const report = {
        timeframe,
        timestamp: Date.now(),
        overall: overallStats,
        coins: coinStats,
        summary: {
          totalActiveTraders: overallStats?.active_traders || 0,
          totalPositions: (overallStats?.long_positions || 0) + (overallStats?.short_positions || 0),
          longBias: overallStats?.long_percentage || 0,
          shortBias: overallStats?.short_percentage || 0,
          topCoins: coinStats
            .sort((a, b) => b.total_volume - a.total_volume)
            .slice(0, 5)
            .map(coin => ({
              coin: coin.coin,
              longPercentage: coin.long_percentage,
              volume: coin.total_volume
            }))
        }
      };
      
      return report;
    } catch (error) {
      logger.error('Error generating report:', error);
      throw error;
    }
  }

  // 解析時間框架
  parseTimeframe(timeframe) {
    const timeframeMap = {
      '1h': 1/24,
      '4h': 4/24,
      '24h': 1,
      '7d': 7,
      '30d': 30
    };
    
    return timeframeMap[timeframe] || 1/24;
  }

  // 獲取優秀交易員列表
  getTopTraders(timeframe = '1h') {
    return Array.from(this.topTraders.get(timeframe) || []);
  }

  // 檢查是否是優秀交易員
  isTopTrader(address, timeframe = '1h') {
    const traders = this.topTraders.get(timeframe);
    return traders ? traders.has(address) : false;
  }
}

export default RealTimeStats;
