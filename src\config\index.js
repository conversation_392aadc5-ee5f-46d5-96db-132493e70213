import dotenv from 'dotenv';

dotenv.config();

export const config = {
  // 服務器配置
  server: {
    port: process.env.PORT || 8080,
    nodeEnv: process.env.NODE_ENV || 'development',
    corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:8080'
  },

  // Hyperliquid API 配置
  hyperliquid: {
    apiUrl: process.env.HYPERLIQUID_API_URL || 'https://api.hyperliquid.xyz',
    wsUrl: process.env.HYPERLIQUID_WS_URL || 'wss://api.hyperliquid.xyz/ws',
    testnetApiUrl: process.env.HYPERLIQUID_TESTNET_API_URL || 'https://api.hyperliquid-testnet.xyz',
    testnetWsUrl: process.env.HYPERLIQUID_TESTNET_WS_URL || 'wss://api.hyperliquid-testnet.xyz/ws',
    useTestnet: process.env.USE_TESTNET === 'true'
  },

  // 數據庫配置
  database: {
    path: process.env.DATABASE_PATH || './data/traders.db'
  },

  // Redis 配置
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT) || 6379,
    password: process.env.REDIS_PASSWORD || '',
    db: parseInt(process.env.REDIS_DB) || 0,
    enabled: process.env.REDIS_ENABLED === 'true'
  },

  // 日誌配置
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || './logs/app.log'
  },

  // 交易員評估配置
  evaluation: {
    minTradesForEvaluation: parseInt(process.env.MIN_TRADES_FOR_EVALUATION) || 10,
    evaluationPeriodDays: parseInt(process.env.EVALUATION_PERIOD_DAYS) || 30,
    topTradersCount: parseInt(process.env.TOP_TRADERS_COUNT) || 100,
    
    // 評估權重
    weights: {
      winRate: 0.25,        // 勝率權重
      profitability: 0.30,  // 盈利能力權重
      sharpeRatio: 0.20,    // 夏普比率權重
      maxDrawdown: 0.15,    // 最大回撤權重
      consistency: 0.10     // 一致性權重
    },

    // 篩選閾值
    thresholds: {
      minWinRate: 0.55,           // 最小勝率 55%
      minProfitability: 0.10,     // 最小收益率 10%
      maxDrawdown: 0.30,          // 最大回撤 30%
      minSharpeRatio: 1.0,        // 最小夏普比率
      minTradingVolume: 10000     // 最小交易量 $10,000
    }
  },

  // 統計配置 (調整為符合 API 限制)
  stats: {
    updateIntervalMs: parseInt(process.env.STATS_UPDATE_INTERVAL_MS) || 300000, // 5 分鐘
    positionTrackingIntervalMs: parseInt(process.env.POSITION_TRACKING_INTERVAL_MS) || 600000, // 10 分鐘
    timeframes: ['1h', '4h', '24h', '7d', '30d']
  },

  // 緩存配置
  cache: {
    ttlSeconds: parseInt(process.env.CACHE_TTL_SECONDS) || 300,
    traderDataTtl: parseInt(process.env.TRADER_DATA_CACHE_TTL) || 3600
  },

  // API 限制 (基於 Hyperliquid 官方限制)
  api: {
    // REST API 限制
    restRateLimit: {
      maxWeightPerMinute: 1200,  // 每分鐘最大權重
      infoRequestWeight: 20,     // info 請求權重
      exchangeRequestWeight: 1,  // exchange 請求基礎權重
      requestIntervalMs: 5000    // 請求間隔 5 秒
    },

    // WebSocket 限制
    websocketLimits: {
      maxConnections: 100,       // 最大連接數
      maxSubscriptions: 1000,    // 最大訂閱數
      maxMessagesPerMinute: 2000, // 每分鐘最大消息數
      maxPostMessages: 100       // 最大同時 post 消息數
    },

    // 地址限制
    addressLimits: {
      initialBuffer: 10000,      // 初始緩衝請求數
      requestsPerUSDC: 1,        // 每 1 USDC 交易量允許 1 個請求
      fallbackRateMs: 10000      // 達到限制時每 10 秒 1 個請求
    }
  },

  // 支持的幣種
  supportedCoins: [
    'BTC', 'ETH', 'SOL', 'AVAX', 'MATIC', 'ARB', 'OP', 'ATOM', 'NEAR', 'FTM',
    'LTC', 'BCH', 'LINK', 'UNI', 'AAVE', 'MKR', 'CRV', 'SUSHI', 'COMP', 'YFI'
  ]
};

export default config;
