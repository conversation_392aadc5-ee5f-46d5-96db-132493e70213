import dotenv from 'dotenv';

dotenv.config();

export const config = {
  // 服務器配置
  server: {
    port: process.env.PORT || 3000,
    nodeEnv: process.env.NODE_ENV || 'development',
    corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:3000'
  },

  // Hyperliquid API 配置
  hyperliquid: {
    apiUrl: process.env.HYPERLIQUID_API_URL || 'https://api.hyperliquid.xyz',
    wsUrl: process.env.HYPERLIQUID_WS_URL || 'wss://api.hyperliquid.xyz/ws',
    testnetApiUrl: process.env.HYPERLIQUID_TESTNET_API_URL || 'https://api.hyperliquid-testnet.xyz',
    testnetWsUrl: process.env.HYPERLIQUID_TESTNET_WS_URL || 'wss://api.hyperliquid-testnet.xyz/ws',
    useTestnet: process.env.USE_TESTNET === 'true'
  },

  // 數據庫配置
  database: {
    path: process.env.DATABASE_PATH || './data/traders.db'
  },

  // Redis 配置
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT) || 6379,
    password: process.env.REDIS_PASSWORD || '',
    db: parseInt(process.env.REDIS_DB) || 0,
    enabled: process.env.REDIS_ENABLED !== 'false'
  },

  // 日誌配置
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || './logs/app.log'
  },

  // 交易員評估配置
  evaluation: {
    minTradesForEvaluation: parseInt(process.env.MIN_TRADES_FOR_EVALUATION) || 10,
    evaluationPeriodDays: parseInt(process.env.EVALUATION_PERIOD_DAYS) || 30,
    topTradersCount: parseInt(process.env.TOP_TRADERS_COUNT) || 100,
    
    // 評估權重
    weights: {
      winRate: 0.25,        // 勝率權重
      profitability: 0.30,  // 盈利能力權重
      sharpeRatio: 0.20,    // 夏普比率權重
      maxDrawdown: 0.15,    // 最大回撤權重
      consistency: 0.10     // 一致性權重
    },

    // 篩選閾值
    thresholds: {
      minWinRate: 0.55,           // 最小勝率 55%
      minProfitability: 0.10,     // 最小收益率 10%
      maxDrawdown: 0.30,          // 最大回撤 30%
      minSharpeRatio: 1.0,        // 最小夏普比率
      minTradingVolume: 10000     // 最小交易量 $10,000
    }
  },

  // 統計配置
  stats: {
    updateIntervalMs: parseInt(process.env.STATS_UPDATE_INTERVAL_MS) || 60000,
    positionTrackingIntervalMs: parseInt(process.env.POSITION_TRACKING_INTERVAL_MS) || 30000,
    timeframes: ['1h', '4h', '24h', '7d', '30d']
  },

  // 緩存配置
  cache: {
    ttlSeconds: parseInt(process.env.CACHE_TTL_SECONDS) || 300,
    traderDataTtl: parseInt(process.env.TRADER_DATA_CACHE_TTL) || 3600
  },

  // API 限制
  api: {
    rateLimit: parseInt(process.env.API_RATE_LIMIT) || 100
  },

  // 支持的幣種
  supportedCoins: [
    'BTC', 'ETH', 'SOL', 'AVAX', 'MATIC', 'ARB', 'OP', 'ATOM', 'NEAR', 'FTM',
    'LTC', 'BCH', 'LINK', 'UNI', 'AAVE', 'MKR', 'CRV', 'SUSHI', 'COMP', 'YFI'
  ]
};

export default config;
