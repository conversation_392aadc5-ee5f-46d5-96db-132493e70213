console.log('Step 1: Starting script');

import dotenv from 'dotenv';
console.log('Step 2: dotenv imported');

dotenv.config();
console.log('Step 3: dotenv configured');

import TelegramBot from 'node-telegram-bot-api';
console.log('Step 4: TelegramBot imported');

console.log('Step 5: Checking token...');
if (!process.env.TELEGRAM_BOT_TOKEN) {
  console.error('❌ No token found');
  process.exit(1);
}
console.log('Step 6: Token found, length:', process.env.TELEGRAM_BOT_TOKEN.length);

console.log('Step 7: Creating bot...');
const bot = new TelegramBot(process.env.TELEGRAM_BOT_TOKEN, { 
  polling: true
});
console.log('Step 8: Bot created');

console.log('Step 9: Getting bot info...');
bot.getMe().then(me => {
  console.log('Step 10: Bot info received:', me.username);
  console.log('🎉 Bo<PERSON> is ready!');
}).catch(error => {
  console.error('❌ getMe failed:', error.message);
});

bot.onText(/\/start/, (msg) => {
  console.log('Received /start');
  bot.sendMessage(msg.chat.id, 'Hello!');
});

console.log('Step 11: Event handlers set up');
console.log('🚀 Script completed, bot should be running...');
