import TelegramBot from 'node-telegram-bot-api';
import dotenv from 'dotenv';
import fs from 'fs';
import database from './src/models/database.js';

dotenv.config();

// 創建日誌函數
function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `${timestamp} [FINAL-BOT]: ${message}\n`;
  console.log(message);
  fs.appendFileSync('logs/final-bot.log', logMessage);
}

// 處理 top 命令的函數
async function handleTopCommand(bot, msg, text) {
  const chatId = msg.chat.id;
  
  try {
    log('📊 Processing top traders request...');
    
    // 解析參數
    const args = text.split(' ').slice(1); // 移除 '/top'
    let limit = 10;
    let timeframe = '24h';
    
    if (args.length > 0) {
      const firstArg = args[0];
      if (['1h', '4h', '24h', '7d', '30d'].includes(firstArg)) {
        timeframe = firstArg;
      } else if (!isNaN(firstArg)) {
        limit = Math.min(parseInt(firstArg), 20);
      }
    }
    
    if (args.length > 1) {
      const secondArg = args[1];
      if (['1h', '4h', '24h', '7d', '30d'].includes(secondArg)) {
        timeframe = secondArg;
      }
    }
    
    log(`📊 Querying top ${limit} traders for ${timeframe}...`);
    
    // 初始化數據庫
    await database.initialize();
    
    // 首先嘗試從 trader_rankings 表查詢
    let traders = await database.all(`
      SELECT 
        tr.trader_address as address,
        tr.rank_position,
        tr.score,
        t.total_trades,
        t.win_rate,
        t.total_pnl,
        t.total_volume
      FROM trader_rankings tr
      JOIN traders t ON tr.trader_address = t.address
      WHERE tr.timeframe = ?
      ORDER BY tr.rank_position ASC 
      LIMIT ?
    `, [timeframe, limit]);
    
    // 如果沒有排名數據，嘗試直接從 traders 表查詢
    if (!traders || traders.length === 0) {
      log('📊 No ranking data found, querying traders table directly...');
      traders = await database.all(`
        SELECT 
          address,
          total_trades,
          win_rate,
          total_pnl,
          total_volume,
          performance_score as score
        FROM traders 
        WHERE is_active = 1 AND total_trades > 0
        ORDER BY total_pnl DESC 
        LIMIT ?
      `, [limit]);
    }
    
    if (!traders || traders.length === 0) {
      await bot.sendMessage(chatId, `📊 暫無 ${timeframe} 時間範圍的交易員數據。\n\n💡 系統可能正在收集數據，請稍後再試。`);
      return;
    }
    
    // 格式化消息
    let message = `📊 <b>頂級交易員排行榜 (${timeframe})</b>\n\n`;
    
    traders.forEach((trader, index) => {
      const rank = trader.rank_position || (index + 1);
      const emoji = rank === 1 ? '🥇' : rank === 2 ? '🥈' : rank === 3 ? '🥉' : '📈';
      const address = trader.address.slice(0, 6) + '...' + trader.address.slice(-4);
      const pnl = trader.total_pnl ? `$${trader.total_pnl.toFixed(2)}` : 'N/A';
      const winRate = trader.win_rate ? `${(trader.win_rate * 100).toFixed(1)}%` : 'N/A';
      const trades = trader.total_trades || 0;
      const volume = trader.total_volume ? `$${(trader.total_volume / 1000).toFixed(1)}K` : 'N/A';
      
      message += `${emoji} <b>#${rank}</b> <code>${address}</code>\n`;
      message += `   💰 PnL: ${pnl}\n`;
      message += `   📊 勝率: ${winRate}\n`;
      message += `   🔄 交易數: ${trades}\n`;
      message += `   📈 交易量: ${volume}\n\n`;
    });
    
    message += `⏰ 時間範圍: ${timeframe}\n`;
    message += `📊 顯示: ${traders.length} 名交易員\n`;
    message += `📅 更新時間: ${new Date().toLocaleString('zh-TW')}`;
    
    await bot.sendMessage(chatId, message, { parse_mode: 'HTML' });
    log(`✅ Sent top traders list (${traders.length} traders)`);
    
  } catch (error) {
    log(`❌ Error in top command: ${error.message}`);
    await bot.sendMessage(chatId, '❌ 獲取交易員數據時發生錯誤，請稍後再試。');
  }
}

// 處理 stats 命令的函數
async function handleStatsCommand(bot, msg, text) {
  const chatId = msg.chat.id;
  
  try {
    log('📈 Processing stats request...');
    
    await database.initialize();
    
    // 獲取基本統計
    const totalTraders = await database.get('SELECT COUNT(*) as count FROM traders WHERE is_active = 1');
    const totalTrades = await database.get('SELECT SUM(total_trades) as total FROM traders WHERE is_active = 1');
    const totalVolume = await database.get('SELECT SUM(total_volume) as total FROM traders WHERE is_active = 1');
    const avgWinRate = await database.get('SELECT AVG(win_rate) as avg FROM traders WHERE is_active = 1 AND win_rate > 0');
    
    const statsMsg = `📈 <b>交易統計概覽</b>\n\n` +
      `👥 活躍交易員: ${totalTraders?.count || 0}\n` +
      `🔄 總交易數: ${totalTrades?.total || 0}\n` +
      `💰 總交易量: $${((totalVolume?.total || 0) / 1000000).toFixed(2)}M\n` +
      `📊 平均勝率: ${((avgWinRate?.avg || 0) * 100).toFixed(1)}%\n\n` +
      `📅 統計時間: ${new Date().toLocaleString('zh-TW')}`;
    
    await bot.sendMessage(chatId, statsMsg, { parse_mode: 'HTML' });
    log('✅ Sent stats message');
    
  } catch (error) {
    log(`❌ Error in stats command: ${error.message}`);
    await bot.sendMessage(chatId, '❌ 獲取統計數據時發生錯誤，請稍後再試。');
  }
}

async function startFinalBot() {
  try {
    log('🚀 Starting final Telegram bot with database integration...');
    
    const token = process.env.TELEGRAM_BOT_TOKEN;
    if (!token) {
      log('❌ TELEGRAM_BOT_TOKEN is required');
      process.exit(1);
    }
    
    // 清理 webhook
    const tempBot = new TelegramBot(token, { polling: false });
    try {
      await tempBot.deleteWebHook();
      log('✅ Webhooks cleared');
    } catch (error) {
      log(`⚠️ Webhook clear warning: ${error.message}`);
    }
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 創建機器人
    const bot = new TelegramBot(token, { 
      polling: {
        interval: 1000,
        autoStart: true,
        params: { timeout: 10 }
      }
    });
    
    log('📝 Setting up message handlers...');
    
    bot.on('message', async (msg) => {
      try {
        const chatId = msg.chat.id;
        const text = msg.text;
        const userName = msg.from.first_name || 'Unknown';
        
        log(`📨 Message from ${userName} (${msg.from.id}): ${text}`);
        
        if (text === '/start') {
          const welcomeMsg = `🎉 歡迎使用 Hyperliquid 交易員追蹤機器人！

📊 可用命令：
/start - 顯示歡迎訊息
/help - 顯示幫助訊息  
/top - 查看頂級交易員排行榜
/top 5 - 查看前5名交易員
/top 1h - 查看1小時時間範圍的排行榜
/stats - 查看交易統計概覽
/status - 查看機器人狀態

🔄 機器人正在運行中，數據來自真實的 Hyperliquid 交易數據...`;
          
          await bot.sendMessage(chatId, welcomeMsg);
          log(`✅ Sent welcome message to ${userName}`);
          
        } else if (text === '/help') {
          const helpMsg = `📖 Hyperliquid 交易員追蹤機器人幫助

🎯 主要功能：
• 即時追蹤優秀的 Hyperliquid 交易員
• 提供交易員排行榜和統計數據
• 支援多種時間範圍查詢

📊 命令說明：
/top - 預設顯示前10名交易員 (24小時)
/top [數量] - 顯示指定數量的交易員 (最多20名)
/top [時間] - 顯示指定時間範圍的排行榜
  • 1h - 1小時
  • 4h - 4小時  
  • 24h - 24小時 (預設)
  • 7d - 7天
  • 30d - 30天

💡 範例：
/top 5 - 前5名交易員
/top 1h - 1小時排行榜
/top 15 7d - 前15名交易員 (7天範圍)

📈 其他命令：
/stats - 查看整體交易統計
/status - 查看機器人運行狀態`;
          
          await bot.sendMessage(chatId, helpMsg);
          log(`✅ Sent help message to ${userName}`);
          
        } else if (text === '/status') {
          const statusMsg = `🔄 機器人狀態報告

✅ 機器人運行中
🕐 當前時間: ${new Date().toLocaleString('zh-TW')}
🤖 機器人: @hyperliquid_copy_bot
👤 用戶: ${userName} (${msg.from.id})
💬 聊天ID: ${chatId}

📊 系統狀態: 正常
🔗 數據庫連接: 正常
📡 Telegram連接: 正常
💾 數據來源: Hyperliquid API`;
          
          await bot.sendMessage(chatId, statusMsg);
          log(`✅ Sent status message to ${userName}`);
          
        } else if (text === '/top' || text.startsWith('/top ')) {
          await handleTopCommand(bot, msg, text);
          
        } else if (text === '/stats' || text.startsWith('/stats ')) {
          await handleStatsCommand(bot, msg, text);
          
        } else {
          await bot.sendMessage(chatId, `🔄 收到訊息: ${text}\n\n輸入 /help 查看可用命令`);
          log(`🔄 Echoed message to ${userName}`);
        }
        
      } catch (error) {
        log(`❌ Error handling message: ${error.message}`);
      }
    });
    
    bot.on('error', (error) => {
      log(`❌ Bot error: ${error.message}`);
    });
    
    bot.on('polling_error', (error) => {
      log(`❌ Polling error: ${error.message}`);
    });
    
    log('✅ Final bot is running and ready with database integration!');
    
    // 每分鐘輸出狀態
    setInterval(() => {
      log(`🔄 Bot status: Running at ${new Date().toLocaleString('zh-TW')}`);
    }, 60000);
    
  } catch (error) {
    log(`❌ Failed to start final bot: ${error.message}`);
    process.exit(1);
  }
}

startFinalBot();
