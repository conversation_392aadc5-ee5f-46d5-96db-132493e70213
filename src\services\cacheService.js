import redis from 'redis';
import { config } from '../config/index.js';
import logger from '../utils/logger.js';

class CacheService {
  constructor() {
    this.client = null;
    this.isConnected = false;
    this.enabled = config.redis.enabled;
    
    // 內存緩存作為備用
    this.memoryCache = new Map();
    this.memoryCacheExpiry = new Map();
  }

  async initialize() {
    if (!this.enabled) {
      logger.info('Redis cache disabled, using memory cache only');
      return;
    }

    try {
      this.client = redis.createClient({
        host: config.redis.host,
        port: config.redis.port,
        password: config.redis.password,
        db: config.redis.db,
        retry_strategy: (options) => {
          if (options.error && options.error.code === 'ECONNREFUSED') {
            logger.error('Redis connection refused');
            return new Error('Redis connection refused');
          }
          if (options.total_retry_time > 1000 * 60 * 60) {
            logger.error('Redis retry time exhausted');
            return new Error('Retry time exhausted');
          }
          if (options.attempt > 10) {
            logger.error('Redis max retry attempts reached');
            return undefined;
          }
          return Math.min(options.attempt * 100, 3000);
        }
      });

      this.client.on('connect', () => {
        logger.info('Connected to Redis cache');
        this.isConnected = true;
      });

      this.client.on('error', (err) => {
        logger.error('Redis cache error:', err);
        this.isConnected = false;
      });

      this.client.on('end', () => {
        logger.warn('Redis connection ended');
        this.isConnected = false;
      });

      await this.client.connect();
      
      // 測試連接
      await this.client.ping();
      logger.info('Redis cache initialized successfully');
      
    } catch (error) {
      logger.error('Failed to initialize Redis cache:', error);
      logger.info('Falling back to memory cache');
      this.enabled = false;
    }
  }

  async disconnect() {
    if (this.client && this.isConnected) {
      await this.client.disconnect();
      logger.info('Redis cache disconnected');
    }
  }

  // 設置緩存
  async set(key, value, ttlSeconds = null) {
    try {
      const serializedValue = JSON.stringify(value);
      const ttl = ttlSeconds || config.cache.ttlSeconds;

      if (this.enabled && this.isConnected) {
        await this.client.setEx(key, ttl, serializedValue);
      } else {
        // 使用內存緩存
        this.memoryCache.set(key, serializedValue);
        this.memoryCacheExpiry.set(key, Date.now() + (ttl * 1000));
      }

      logger.debug(`Cache set: ${key} (TTL: ${ttl}s)`);
    } catch (error) {
      logger.error(`Error setting cache for key ${key}:`, error);
    }
  }

  // 獲取緩存
  async get(key) {
    try {
      let value = null;

      if (this.enabled && this.isConnected) {
        value = await this.client.get(key);
      } else {
        // 檢查內存緩存
        const expiry = this.memoryCacheExpiry.get(key);
        if (expiry && Date.now() < expiry) {
          value = this.memoryCache.get(key);
        } else if (expiry) {
          // 過期，清除
          this.memoryCache.delete(key);
          this.memoryCacheExpiry.delete(key);
        }
      }

      if (value) {
        logger.debug(`Cache hit: ${key}`);
        return JSON.parse(value);
      } else {
        logger.debug(`Cache miss: ${key}`);
        return null;
      }
    } catch (error) {
      logger.error(`Error getting cache for key ${key}:`, error);
      return null;
    }
  }

  // 刪除緩存
  async del(key) {
    try {
      if (this.enabled && this.isConnected) {
        await this.client.del(key);
      } else {
        this.memoryCache.delete(key);
        this.memoryCacheExpiry.delete(key);
      }
      logger.debug(`Cache deleted: ${key}`);
    } catch (error) {
      logger.error(`Error deleting cache for key ${key}:`, error);
    }
  }

  // 檢查緩存是否存在
  async exists(key) {
    try {
      if (this.enabled && this.isConnected) {
        return await this.client.exists(key) === 1;
      } else {
        const expiry = this.memoryCacheExpiry.get(key);
        return expiry && Date.now() < expiry;
      }
    } catch (error) {
      logger.error(`Error checking cache existence for key ${key}:`, error);
      return false;
    }
  }

  // 設置過期時間
  async expire(key, ttlSeconds) {
    try {
      if (this.enabled && this.isConnected) {
        await this.client.expire(key, ttlSeconds);
      } else {
        this.memoryCacheExpiry.set(key, Date.now() + (ttlSeconds * 1000));
      }
    } catch (error) {
      logger.error(`Error setting expiry for key ${key}:`, error);
    }
  }

  // 清除所有緩存
  async flush() {
    try {
      if (this.enabled && this.isConnected) {
        await this.client.flushDb();
      } else {
        this.memoryCache.clear();
        this.memoryCacheExpiry.clear();
      }
      logger.info('Cache flushed');
    } catch (error) {
      logger.error('Error flushing cache:', error);
    }
  }

  // 獲取緩存統計
  async getStats() {
    try {
      if (this.enabled && this.isConnected) {
        const info = await this.client.info('memory');
        return {
          type: 'redis',
          connected: this.isConnected,
          info: info
        };
      } else {
        return {
          type: 'memory',
          connected: true,
          size: this.memoryCache.size,
          keys: Array.from(this.memoryCache.keys())
        };
      }
    } catch (error) {
      logger.error('Error getting cache stats:', error);
      return { type: 'unknown', connected: false };
    }
  }

  // 緩存裝飾器方法
  async cached(key, fetchFunction, ttlSeconds = null) {
    try {
      // 先嘗試從緩存獲取
      let result = await this.get(key);
      
      if (result !== null) {
        return result;
      }

      // 緩存未命中，執行獲取函數
      result = await fetchFunction();
      
      // 存入緩存
      if (result !== null && result !== undefined) {
        await this.set(key, result, ttlSeconds);
      }

      return result;
    } catch (error) {
      logger.error(`Error in cached function for key ${key}:`, error);
      // 如果緩存出錯，直接執行獲取函數
      return await fetchFunction();
    }
  }

  // 批量設置
  async mset(keyValuePairs, ttlSeconds = null) {
    try {
      const ttl = ttlSeconds || config.cache.ttlSeconds;

      if (this.enabled && this.isConnected) {
        const pipeline = this.client.multi();
        for (const [key, value] of keyValuePairs) {
          pipeline.setEx(key, ttl, JSON.stringify(value));
        }
        await pipeline.exec();
      } else {
        const expiry = Date.now() + (ttl * 1000);
        for (const [key, value] of keyValuePairs) {
          this.memoryCache.set(key, JSON.stringify(value));
          this.memoryCacheExpiry.set(key, expiry);
        }
      }

      logger.debug(`Batch cache set: ${keyValuePairs.length} items`);
    } catch (error) {
      logger.error('Error in batch cache set:', error);
    }
  }

  // 批量獲取
  async mget(keys) {
    try {
      const results = {};

      if (this.enabled && this.isConnected) {
        const values = await this.client.mGet(keys);
        for (let i = 0; i < keys.length; i++) {
          if (values[i]) {
            results[keys[i]] = JSON.parse(values[i]);
          }
        }
      } else {
        const now = Date.now();
        for (const key of keys) {
          const expiry = this.memoryCacheExpiry.get(key);
          if (expiry && now < expiry) {
            const value = this.memoryCache.get(key);
            if (value) {
              results[key] = JSON.parse(value);
            }
          }
        }
      }

      return results;
    } catch (error) {
      logger.error('Error in batch cache get:', error);
      return {};
    }
  }

  // 清理過期的內存緩存
  cleanupMemoryCache() {
    if (this.enabled) return; // Redis 自動處理過期

    const now = Date.now();
    const expiredKeys = [];

    for (const [key, expiry] of this.memoryCacheExpiry) {
      if (now >= expiry) {
        expiredKeys.push(key);
      }
    }

    for (const key of expiredKeys) {
      this.memoryCache.delete(key);
      this.memoryCacheExpiry.delete(key);
    }

    if (expiredKeys.length > 0) {
      logger.debug(`Cleaned up ${expiredKeys.length} expired cache entries`);
    }
  }

  // 生成緩存鍵
  generateKey(prefix, ...parts) {
    return `${prefix}:${parts.join(':')}`;
  }

  // 常用的緩存鍵生成器
  keys = {
    traderStats: (address, timeframe) => this.generateKey('trader_stats', address, timeframe),
    traderRankings: (timeframe) => this.generateKey('rankings', timeframe),
    realTimeStats: (timeframe, coin) => this.generateKey('rt_stats', timeframe, coin || 'all'),
    traderEvaluation: (address, timeframe) => this.generateKey('evaluation', address, timeframe),
    marketData: (coin) => this.generateKey('market', coin),
    topTraders: (timeframe) => this.generateKey('top_traders', timeframe)
  };
}

// 創建單例實例
const cacheService = new CacheService();

export default cacheService;
