import logger from '../../utils/logger.js';

// 用戶認證中間件
async function authMiddleware(msg, bot) {
  try {
    const user = msg.from;
    const chatId = msg.chat.id;

    // 檢查是否為私聊
    if (msg.chat.type !== 'private') {
      await bot.bot.sendMessage(chatId, 
        '❌ 此機器人僅支持私聊使用，請私信我使用所有功能。'
      );
      return false;
    }

    // 檢查用戶是否被封禁（如果有封禁系統的話）
    const userRecord = await bot.getUser(user.id);
    if (userRecord && userRecord.is_banned) {
      await bot.bot.sendMessage(chatId, 
        '❌ 您的賬戶已被暫停使用，如有疑問請聯繫管理員。'
      );
      return false;
    }

    // 創建或更新用戶記錄
    await bot.upsertUser(user);

    return true;
  } catch (error) {
    logger.error('Auth middleware error:', error);
    return false;
  }
}

export default authMiddleware;
