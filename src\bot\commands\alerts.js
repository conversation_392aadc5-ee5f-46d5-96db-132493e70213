import { subscriptionTypes } from '../config/index.js';
import logger from '../../utils/logger.js';
import database from '../../models/database.js';

class AlertCommands {
  constructor(bot) {
    this.bot = bot;
  }

  async alerts(msg, args) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    try {
      // 獲取用戶當前訂閱
      const userRecord = await this.bot.getUser(userId);
      if (!userRecord) {
        await this.bot.sendErrorMessage(chatId, '用戶信息獲取失敗。');
        return;
      }

      const subscriptions = await this.getUserSubscriptions(userRecord.id);

      // 格式化通知設置消息
      const message = this.formatAlertsMessage(subscriptions);

      // 創建通知管理鍵盤
      const keyboard = this.createAlertsKeyboard(subscriptions);

      await this.bot.bot.sendMessage(chatId, message, {
        reply_markup: keyboard,
        parse_mode: 'HTML'
      });

    } catch (error) {
      logger.error('Error in alerts command:', error);
      await this.bot.sendErrorMessage(chatId, '獲取通知設置失敗。');
    }
  }

  async subscribe(msg, args) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    try {
      if (args.length === 0) {
        await this.bot.bot.sendMessage(chatId, 
          '❌ 請指定訂閱類型。\n\n可用類型:\n' +
          Object.keys(subscriptionTypes).map(type => 
            `• ${type} - ${subscriptionTypes[type].name}`
          ).join('\n') +
          '\n\n用法: /subscribe top_traders'
        );
        return;
      }

      const subscriptionType = args[0];
      
      if (!subscriptionTypes[subscriptionType]) {
        await this.bot.bot.sendMessage(chatId, 
          '❌ 無效的訂閱類型。使用 /alerts 查看可用選項。'
        );
        return;
      }

      // 獲取用戶記錄
      const userRecord = await this.bot.getUser(userId);
      if (!userRecord) {
        await this.bot.sendErrorMessage(chatId, '用戶信息獲取失敗。');
        return;
      }

      // 檢查是否已經訂閱
      const existingSubscription = await this.getSubscription(userRecord.id, subscriptionType);
      
      if (existingSubscription && existingSubscription.is_active) {
        await this.bot.bot.sendMessage(chatId, 
          `✅ 您已經訂閱了 "${subscriptionTypes[subscriptionType].name}"。`
        );
        return;
      }

      // 創建或激活訂閱
      await this.createSubscription(userRecord.id, subscriptionType);

      await this.bot.bot.sendMessage(chatId, 
        `🔔 成功訂閱 "${subscriptionTypes[subscriptionType].name}"！\n\n` +
        `${subscriptionTypes[subscriptionType].description}\n\n` +
        `使用 /alerts 管理您的所有訂閱。`
      );

      logger.info(`User ${userId} subscribed to ${subscriptionType}`);

    } catch (error) {
      logger.error('Error in subscribe command:', error);
      await this.bot.sendErrorMessage(chatId, '訂閱失敗，請稍後重試。');
    }
  }

  async unsubscribe(msg, args) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    try {
      if (args.length === 0) {
        await this.bot.bot.sendMessage(chatId, 
          '❌ 請指定要取消的訂閱類型。\n用法: /unsubscribe top_traders'
        );
        return;
      }

      const subscriptionType = args[0];
      
      if (!subscriptionTypes[subscriptionType]) {
        await this.bot.bot.sendMessage(chatId, 
          '❌ 無效的訂閱類型。使用 /alerts 查看當前訂閱。'
        );
        return;
      }

      // 獲取用戶記錄
      const userRecord = await this.bot.getUser(userId);
      if (!userRecord) {
        await this.bot.sendErrorMessage(chatId, '用戶信息獲取失敗。');
        return;
      }

      // 檢查訂閱是否存在
      const subscription = await this.getSubscription(userRecord.id, subscriptionType);
      
      if (!subscription || !subscription.is_active) {
        await this.bot.bot.sendMessage(chatId, 
          `❌ 您沒有訂閱 "${subscriptionTypes[subscriptionType].name}"。`
        );
        return;
      }

      // 取消訂閱
      await this.cancelSubscription(userRecord.id, subscriptionType);

      await this.bot.bot.sendMessage(chatId, 
        `✅ 已取消訂閱 "${subscriptionTypes[subscriptionType].name}"。`
      );

      logger.info(`User ${userId} unsubscribed from ${subscriptionType}`);

    } catch (error) {
      logger.error('Error in unsubscribe command:', error);
      await this.bot.sendErrorMessage(chatId, '取消訂閱失敗，請稍後重試。');
    }
  }

  async getUserSubscriptions(userId) {
    try {
      return await database.all(`
        SELECT * FROM bot_subscriptions 
        WHERE user_id = ? AND is_active = 1
        ORDER BY created_at DESC
      `, [userId]);
    } catch (error) {
      logger.error('Error getting user subscriptions:', error);
      throw error;
    }
  }

  async getSubscription(userId, type) {
    try {
      return await database.get(`
        SELECT * FROM bot_subscriptions 
        WHERE user_id = ? AND type = ?
      `, [userId, type]);
    } catch (error) {
      logger.error('Error getting subscription:', error);
      throw error;
    }
  }

  async createSubscription(userId, type, config = {}) {
    try {
      await database.run(`
        INSERT OR REPLACE INTO bot_subscriptions 
        (user_id, type, config, is_active, created_at)
        VALUES (?, ?, ?, 1, ?)
      `, [userId, type, JSON.stringify(config), Date.now()]);
    } catch (error) {
      logger.error('Error creating subscription:', error);
      throw error;
    }
  }

  async cancelSubscription(userId, type) {
    try {
      await database.run(`
        UPDATE bot_subscriptions 
        SET is_active = 0 
        WHERE user_id = ? AND type = ?
      `, [userId, type]);
    } catch (error) {
      logger.error('Error canceling subscription:', error);
      throw error;
    }
  }

  formatAlertsMessage(subscriptions) {
    let message = `🔔 通知設置管理\n\n`;

    if (subscriptions.length === 0) {
      message += `❌ 您目前沒有任何活躍的訂閱。\n\n`;
      message += `📋 可用的通知類型:\n`;
      Object.entries(subscriptionTypes).forEach(([type, info]) => {
        message += `• <b>${info.name}</b>\n  ${info.description}\n\n`;
      });
      message += `💡 使用 /subscribe <類型> 來訂閱通知`;
    } else {
      message += `✅ 您的活躍訂閱:\n\n`;
      subscriptions.forEach(sub => {
        const typeInfo = subscriptionTypes[sub.type];
        if (typeInfo) {
          message += `🔔 <b>${typeInfo.name}</b>\n`;
          message += `   ${typeInfo.description}\n`;
          message += `   訂閱時間: ${new Date(sub.created_at).toLocaleDateString()}\n\n`;
        }
      });

      // 顯示未訂閱的類型
      const subscribedTypes = subscriptions.map(sub => sub.type);
      const unsubscribedTypes = Object.keys(subscriptionTypes).filter(
        type => !subscribedTypes.includes(type)
      );

      if (unsubscribedTypes.length > 0) {
        message += `📋 可訂閱的其他通知:\n`;
        unsubscribedTypes.forEach(type => {
          const info = subscriptionTypes[type];
          message += `• ${info.name}\n`;
        });
      }
    }

    return message;
  }

  createAlertsKeyboard(subscriptions) {
    const keyboard = { inline_keyboard: [] };

    // 訂閱管理按鈕
    const subscribedTypes = subscriptions.map(sub => sub.type);
    const unsubscribedTypes = Object.keys(subscriptionTypes).filter(
      type => !subscribedTypes.includes(type)
    );

    // 快速訂閱按鈕
    if (unsubscribedTypes.length > 0) {
      keyboard.inline_keyboard.push([
        { text: '➕ 快速訂閱', callback_data: 'alerts:quick_subscribe' }
      ]);

      // 顯示前3個未訂閱的類型
      const quickSubscribeRow = [];
      unsubscribedTypes.slice(0, 3).forEach(type => {
        const info = subscriptionTypes[type];
        quickSubscribeRow.push({
          text: `+ ${info.name}`,
          callback_data: `alerts:subscribe:${type}`
        });
      });
      if (quickSubscribeRow.length > 0) {
        keyboard.inline_keyboard.push(quickSubscribeRow);
      }
    }

    // 取消訂閱按鈕
    if (subscribedTypes.length > 0) {
      keyboard.inline_keyboard.push([
        { text: '➖ 管理訂閱', callback_data: 'alerts:manage_subscriptions' }
      ]);

      // 顯示前3個已訂閱的類型
      const unsubscribeRow = [];
      subscribedTypes.slice(0, 3).forEach(type => {
        const info = subscriptionTypes[type];
        unsubscribeRow.push({
          text: `- ${info.name}`,
          callback_data: `alerts:unsubscribe:${type}`
        });
      });
      if (unsubscribeRow.length > 0) {
        keyboard.inline_keyboard.push(unsubscribeRow);
      }
    }

    // 設置按鈕
    keyboard.inline_keyboard.push([
      { text: '⚙️ 通知設置', callback_data: 'alerts:settings' },
      { text: '📊 統計', callback_data: 'stats:24h' }
    ]);

    keyboard.inline_keyboard.push([
      { text: '🏠 返回主頁', callback_data: 'start:main' }
    ]);

    return keyboard;
  }

  // 處理回調查詢的輔助方法
  async handleSubscribeCallback(msg, type) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    try {
      const userRecord = await this.bot.getUser(userId);
      if (!userRecord) return;

      await this.createSubscription(userRecord.id, type);
      
      await this.bot.bot.editMessageText(
        `🔔 成功訂閱 "${subscriptionTypes[type].name}"！`,
        {
          chat_id: chatId,
          message_id: msg.message_id,
          reply_markup: {
            inline_keyboard: [[
              { text: '🔔 管理通知', callback_data: 'alerts:main' },
              { text: '🏠 返回主頁', callback_data: 'start:main' }
            ]]
          }
        }
      );
    } catch (error) {
      logger.error('Error in subscribe callback:', error);
    }
  }

  async handleUnsubscribeCallback(msg, type) {
    const chatId = msg.chat.id;
    const userId = msg.from.id;

    try {
      const userRecord = await this.bot.getUser(userId);
      if (!userRecord) return;

      await this.cancelSubscription(userRecord.id, type);
      
      await this.bot.bot.editMessageText(
        `✅ 已取消訂閱 "${subscriptionTypes[type].name}"。`,
        {
          chat_id: chatId,
          message_id: msg.message_id,
          reply_markup: {
            inline_keyboard: [[
              { text: '🔔 管理通知', callback_data: 'alerts:main' },
              { text: '🏠 返回主頁', callback_data: 'start:main' }
            ]]
          }
        }
      );
    } catch (error) {
      logger.error('Error in unsubscribe callback:', error);
    }
  }

  // 發送通知的方法（供其他服務調用）
  async sendNotification(type, data) {
    try {
      // 獲取訂閱該類型通知的用戶
      const subscribers = await database.all(`
        SELECT bu.telegram_id 
        FROM bot_subscriptions bs
        JOIN bot_users bu ON bs.user_id = bu.id
        WHERE bs.type = ? AND bs.is_active = 1
      `, [type]);

      if (subscribers.length === 0) {
        return;
      }

      // 格式化通知消息
      const message = this.formatNotificationMessage(type, data);

      // 發送給所有訂閱者
      for (const subscriber of subscribers) {
        try {
          await this.bot.bot.sendMessage(subscriber.telegram_id, message, {
            parse_mode: 'HTML'
          });
        } catch (error) {
          logger.error(`Failed to send notification to ${subscriber.telegram_id}:`, error);
        }
      }

      logger.info(`Sent ${type} notification to ${subscribers.length} users`);
    } catch (error) {
      logger.error('Error sending notification:', error);
    }
  }

  formatNotificationMessage(type, data) {
    switch (type) {
      case 'top_traders':
        return `🏆 頂級交易員更新\n\n新的交易員進入排行榜前10名！\n\n使用 /top 查看最新排行榜。`;
      
      case 'volume_alerts':
        return `📊 交易量警報\n\n當前小時交易量: $${data.volume?.toLocaleString()}\n已達到設定閾值！`;
      
      case 'performance_alerts':
        return `🎯 績效警報\n\n檢測到交易員績效重大變化\n\n使用 /performance 查看詳細信息。`;
      
      case 'new_traders':
        return `🆕 發現新的優秀交易員\n\n地址: ${data.address}\n勝率: ${data.winRate}%\n\n使用 /trader ${data.address} 查看詳情。`;
      
      default:
        return `🔔 通知: ${JSON.stringify(data)}`;
    }
  }
}

export default AlertCommands;
