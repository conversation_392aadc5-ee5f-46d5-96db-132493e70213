import TelegramBot from 'node-telegram-bot-api';
import dotenv from 'dotenv';

dotenv.config();

async function testFinalBot() {
  try {
    console.log('🧪 測試最終版本機器人 (含數據庫整合)...');
    
    const bot = new TelegramBot(process.env.TELEGRAM_BOT_TOKEN, { polling: false });
    const chatId = 251366744; // <PERSON>'s chat ID
    
    console.log('📤 發送 /start 命令...');
    await bot.sendMessage(chatId, '/start');
    
    console.log('⏳ 等待 4 秒...');
    await new Promise(resolve => setTimeout(resolve, 4000));
    
    console.log('📤 發送 /top 命令 (測試數據庫查詢)...');
    await bot.sendMessage(chatId, '/top');
    
    console.log('⏳ 等待 5 秒...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    console.log('📤 發送 /top 5 命令...');
    await bot.sendMessage(chatId, '/top 5');
    
    console.log('⏳ 等待 4 秒...');
    await new Promise(resolve => setTimeout(resolve, 4000));
    
    console.log('📤 發送 /stats 命令 (測試統計功能)...');
    await bot.sendMessage(chatId, '/stats');
    
    console.log('⏳ 等待 4 秒...');
    await new Promise(resolve => setTimeout(resolve, 4000));
    
    console.log('📤 發送 /help 命令...');
    await bot.sendMessage(chatId, '/help');
    
    console.log('⏳ 等待 4 秒...');
    await new Promise(resolve => setTimeout(resolve, 4000));
    
    console.log('📤 發送 /status 命令...');
    await bot.sendMessage(chatId, '/status');
    
    console.log('✅ 所有測試命令已發送！');
    console.log('🔍 請檢查 Telegram 中的響應和機器人日誌');
    console.log('📊 特別注意 /top 和 /stats 命令是否顯示真實數據');
    
  } catch (error) {
    console.error('❌ 測試失敗:', error.message);
  }
}

testFinalBot();
