import {
  __commonJS
} from "./chunk-G3PMV62Z.js";

// node_modules/@babel/runtime/helpers/interopRequireDefault.js
var require_interopRequireDefault = __commonJS({
  "node_modules/@babel/runtime/helpers/interopRequireDefault.js"(exports, module) {
    function _interopRequireDefault(e) {
      return e && e.__esModule ? e : {
        "default": e
      };
    }
    module.exports = _interopRequireDefault, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/rc-pagination/lib/locale/zh_TW.js
var require_zh_TW = __commonJS({
  "node_modules/rc-pagination/lib/locale/zh_TW.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var locale = {
      // Options
      items_per_page: "條/頁",
      jump_to: "跳至",
      jump_to_confirm: "確定",
      page: "頁",
      // Pagination
      prev_page: "上一頁",
      next_page: "下一頁",
      prev_5: "向前 5 頁",
      next_5: "向後 5 頁",
      prev_3: "向前 3 頁",
      next_3: "向後 3 頁",
      page_size: "頁碼"
    };
    var _default = exports.default = locale;
  }
});

// node_modules/@babel/runtime/helpers/typeof.js
var require_typeof = __commonJS({
  "node_modules/@babel/runtime/helpers/typeof.js"(exports, module) {
    function _typeof(o) {
      "@babel/helpers - typeof";
      return module.exports = _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o2) {
        return typeof o2;
      } : function(o2) {
        return o2 && "function" == typeof Symbol && o2.constructor === Symbol && o2 !== Symbol.prototype ? "symbol" : typeof o2;
      }, module.exports.__esModule = true, module.exports["default"] = module.exports, _typeof(o);
    }
    module.exports = _typeof, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/toPrimitive.js
var require_toPrimitive = __commonJS({
  "node_modules/@babel/runtime/helpers/toPrimitive.js"(exports, module) {
    var _typeof = require_typeof()["default"];
    function toPrimitive(t, r) {
      if ("object" != _typeof(t) || !t) return t;
      var e = t[Symbol.toPrimitive];
      if (void 0 !== e) {
        var i = e.call(t, r || "default");
        if ("object" != _typeof(i)) return i;
        throw new TypeError("@@toPrimitive must return a primitive value.");
      }
      return ("string" === r ? String : Number)(t);
    }
    module.exports = toPrimitive, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/toPropertyKey.js
var require_toPropertyKey = __commonJS({
  "node_modules/@babel/runtime/helpers/toPropertyKey.js"(exports, module) {
    var _typeof = require_typeof()["default"];
    var toPrimitive = require_toPrimitive();
    function toPropertyKey(t) {
      var i = toPrimitive(t, "string");
      return "symbol" == _typeof(i) ? i : i + "";
    }
    module.exports = toPropertyKey, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/defineProperty.js
var require_defineProperty = __commonJS({
  "node_modules/@babel/runtime/helpers/defineProperty.js"(exports, module) {
    var toPropertyKey = require_toPropertyKey();
    function _defineProperty(e, r, t) {
      return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {
        value: t,
        enumerable: true,
        configurable: true,
        writable: true
      }) : e[r] = t, e;
    }
    module.exports = _defineProperty, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/@babel/runtime/helpers/objectSpread2.js
var require_objectSpread2 = __commonJS({
  "node_modules/@babel/runtime/helpers/objectSpread2.js"(exports, module) {
    var defineProperty = require_defineProperty();
    function ownKeys(e, r) {
      var t = Object.keys(e);
      if (Object.getOwnPropertySymbols) {
        var o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function(r2) {
          return Object.getOwnPropertyDescriptor(e, r2).enumerable;
        })), t.push.apply(t, o);
      }
      return t;
    }
    function _objectSpread2(e) {
      for (var r = 1; r < arguments.length; r++) {
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys(Object(t), true).forEach(function(r2) {
          defineProperty(e, r2, t[r2]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r2) {
          Object.defineProperty(e, r2, Object.getOwnPropertyDescriptor(t, r2));
        });
      }
      return e;
    }
    module.exports = _objectSpread2, module.exports.__esModule = true, module.exports["default"] = module.exports;
  }
});

// node_modules/rc-picker/lib/locale/common.js
var require_common = __commonJS({
  "node_modules/rc-picker/lib/locale/common.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.commonLocale = void 0;
    var commonLocale = exports.commonLocale = {
      yearFormat: "YYYY",
      dayFormat: "D",
      cellMeridiemFormat: "A",
      monthBeforeYear: true
    };
  }
});

// node_modules/rc-picker/lib/locale/zh_TW.js
var require_zh_TW2 = __commonJS({
  "node_modules/rc-picker/lib/locale/zh_TW.js"(exports) {
    "use strict";
    var _interopRequireDefault = require_interopRequireDefault().default;
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _objectSpread2 = _interopRequireDefault(require_objectSpread2());
    var _common = require_common();
    var locale = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, _common.commonLocale), {}, {
      locale: "zh_TW",
      today: "今天",
      now: "此刻",
      backToToday: "返回今天",
      ok: "確定",
      timeSelect: "選擇時間",
      dateSelect: "選擇日期",
      weekSelect: "選擇周",
      clear: "清除",
      week: "週",
      month: "月",
      year: "年",
      previousMonth: "上個月 (翻頁上鍵)",
      nextMonth: "下個月 (翻頁下鍵)",
      monthSelect: "選擇月份",
      yearSelect: "選擇年份",
      decadeSelect: "選擇年代",
      yearFormat: "YYYY年",
      dateFormat: "YYYY年M月D日",
      dateTimeFormat: "YYYY年M月D日 HH時mm分ss秒",
      previousYear: "上一年 (Control鍵加左方向鍵)",
      nextYear: "下一年 (Control鍵加右方向鍵)",
      previousDecade: "上一年代",
      nextDecade: "下一年代",
      previousCentury: "上一世紀",
      nextCentury: "下一世紀",
      cellDateFormat: "D",
      monthBeforeYear: false
    });
    var _default = exports.default = locale;
  }
});

// node_modules/antd/lib/time-picker/locale/zh_TW.js
var require_zh_TW3 = __commonJS({
  "node_modules/antd/lib/time-picker/locale/zh_TW.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var locale = {
      placeholder: "請選擇時間"
    };
    var _default = exports.default = locale;
  }
});

// node_modules/antd/lib/date-picker/locale/zh_TW.js
var require_zh_TW4 = __commonJS({
  "node_modules/antd/lib/date-picker/locale/zh_TW.js"(exports) {
    "use strict";
    var _interopRequireDefault = require_interopRequireDefault().default;
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _zh_TW = _interopRequireDefault(require_zh_TW2());
    var _zh_TW2 = _interopRequireDefault(require_zh_TW3());
    var locale = {
      lang: Object.assign({
        placeholder: "請選擇日期",
        yearPlaceholder: "請選擇年份",
        quarterPlaceholder: "請選擇季度",
        monthPlaceholder: "請選擇月份",
        weekPlaceholder: "請選擇周",
        rangePlaceholder: ["開始日期", "結束日期"],
        rangeYearPlaceholder: ["開始年份", "結束年份"],
        rangeMonthPlaceholder: ["開始月份", "結束月份"],
        rangeQuarterPlaceholder: ["開始季度", "結束季度"],
        rangeWeekPlaceholder: ["開始周", "結束周"]
      }, _zh_TW.default),
      timePickerLocale: Object.assign({}, _zh_TW2.default)
    };
    locale.lang.ok = "確 定";
    var _default = exports.default = locale;
  }
});

// node_modules/antd/lib/calendar/locale/zh_TW.js
var require_zh_TW5 = __commonJS({
  "node_modules/antd/lib/calendar/locale/zh_TW.js"(exports) {
    "use strict";
    var _interopRequireDefault = require_interopRequireDefault().default;
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _zh_TW = _interopRequireDefault(require_zh_TW4());
    var _default = exports.default = _zh_TW.default;
  }
});

// node_modules/antd/lib/locale/zh_TW.js
var require_zh_TW6 = __commonJS({
  "node_modules/antd/lib/locale/zh_TW.js"(exports) {
    "use strict";
    var _interopRequireDefault = require_interopRequireDefault().default;
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _zh_TW = _interopRequireDefault(require_zh_TW());
    var _zh_TW2 = _interopRequireDefault(require_zh_TW5());
    var _zh_TW3 = _interopRequireDefault(require_zh_TW4());
    var _zh_TW4 = _interopRequireDefault(require_zh_TW3());
    var typeTemplate = "${label}不是一個有效的${type}";
    var localeValues = {
      locale: "zh-tw",
      Pagination: _zh_TW.default,
      DatePicker: _zh_TW3.default,
      TimePicker: _zh_TW4.default,
      Calendar: _zh_TW2.default,
      global: {
        placeholder: "請選擇",
        close: "關閉"
      },
      Table: {
        filterTitle: "篩選器",
        filterConfirm: "確定",
        filterReset: "重置",
        filterEmptyText: "無篩選項",
        filterCheckAll: "全選",
        filterSearchPlaceholder: "在篩選項中搜尋",
        emptyText: "暫無數據",
        selectAll: "全部選取",
        selectInvert: "反向選取",
        selectNone: "清空所有",
        selectionAll: "全選所有",
        sortTitle: "排序",
        expand: "展開行",
        collapse: "關閉行",
        triggerDesc: "點擊降序",
        triggerAsc: "點擊升序",
        cancelSort: "取消排序"
      },
      Modal: {
        okText: "確定",
        cancelText: "取消",
        justOkText: "知道了"
      },
      Tour: {
        Next: "下一步",
        Previous: "上一步",
        Finish: "結束導覽"
      },
      Popconfirm: {
        okText: "確定",
        cancelText: "取消"
      },
      Transfer: {
        titles: ["", ""],
        searchPlaceholder: "搜尋資料",
        itemUnit: "項目",
        itemsUnit: "項目",
        remove: "删除",
        selectCurrent: "全選當頁",
        removeCurrent: "删除當頁",
        selectAll: "全選所有",
        removeAll: "删除全部",
        selectInvert: "反選當頁"
      },
      Upload: {
        uploading: "正在上傳...",
        removeFile: "刪除檔案",
        uploadError: "上傳失敗",
        previewFile: "檔案預覽",
        downloadFile: "下载文件"
      },
      Empty: {
        description: "無此資料"
      },
      Icon: {
        icon: "圖標"
      },
      Text: {
        edit: "編輯",
        copy: "複製",
        copied: "複製成功",
        expand: "展開",
        collapse: "收起"
      },
      Form: {
        optional: "（可選）",
        defaultValidateMessages: {
          default: "字段驗證錯誤${label}",
          required: "請輸入${label}",
          enum: "${label}必須是其中一個[${enum}]",
          whitespace: "${label}不能為空字符",
          date: {
            format: "${label}日期格式無效",
            parse: "${label}不能轉換為日期",
            invalid: "${label}是一個無效日期"
          },
          types: {
            string: typeTemplate,
            method: typeTemplate,
            array: typeTemplate,
            object: typeTemplate,
            number: typeTemplate,
            date: typeTemplate,
            boolean: typeTemplate,
            integer: typeTemplate,
            float: typeTemplate,
            regexp: typeTemplate,
            email: typeTemplate,
            url: typeTemplate,
            hex: typeTemplate
          },
          string: {
            len: "${label}須為${len}個字符",
            min: "${label}最少${min}個字符",
            max: "${label}最多${max}個字符",
            range: "${label}須在${min}-${max}字符之間"
          },
          number: {
            len: "${label}必須等於${len}",
            min: "${label}最小值為${min}",
            max: "${label}最大值為${max}",
            range: "${label}須在${min}-${max}之間"
          },
          array: {
            len: "須為${len}個${label}",
            min: "最少${min}個${label}",
            max: "最多${max}個${label}",
            range: "${label}數量須在${min}-${max}之間"
          },
          pattern: {
            mismatch: "${label}與模式不匹配${pattern}"
          }
        }
      },
      Image: {
        preview: "預覽"
      },
      QRCode: {
        expired: "二維碼過期",
        refresh: "點擊刷新",
        scanned: "已掃描"
      },
      ColorPicker: {
        presetEmpty: "暫無",
        transparent: "透明",
        singleColor: "單色",
        gradientColor: "漸變色"
      }
    };
    var _default = exports.default = localeValues;
  }
});

// node_modules/antd/locale/zh_TW.js
var require_zh_TW7 = __commonJS({
  "node_modules/antd/locale/zh_TW.js"(exports, module) {
    module.exports = require_zh_TW6();
  }
});
export default require_zh_TW7();
//# sourceMappingURL=antd_locale_zh_TW.js.map
