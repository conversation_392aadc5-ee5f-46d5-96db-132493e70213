import TelegramBot from 'node-telegram-bot-api';
import dotenv from 'dotenv';
import fs from 'fs';

dotenv.config();

// 創建日誌函數
function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `${timestamp} [WORKING-BOT]: ${message}\n`;
  console.log(message);
  fs.appendFileSync('logs/working-bot.log', logMessage);
}

async function startWorkingBot() {
  try {
    log('🚀 Starting working Telegram bot...');
    
    const token = process.env.TELEGRAM_BOT_TOKEN;
    if (!token) {
      log('❌ TELEGRAM_BOT_TOKEN is required');
      process.exit(1);
    }
    
    // 清理 webhook
    const tempBot = new TelegramBot(token, { polling: false });
    try {
      await tempBot.deleteWebHook();
      log('✅ Webhooks cleared');
    } catch (error) {
      log(`⚠️ Webhook clear warning: ${error.message}`);
    }
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 創建機器人
    const bot = new TelegramBot(token, { 
      polling: {
        interval: 1000,
        autoStart: true,
        params: { timeout: 10 }
      }
    });
    
    log('📝 Setting up message handlers...');
    
    bot.on('message', async (msg) => {
      try {
        const chatId = msg.chat.id;
        const text = msg.text;
        const userName = msg.from.first_name || 'Unknown';
        
        log(`📨 Message from ${userName} (${msg.from.id}): ${text}`);
        
        if (text === '/start') {
          const welcomeMsg = `🎉 歡迎使用 Hyperliquid 交易員追蹤機器人！

📊 可用命令：
/start - 顯示歡迎訊息
/help - 顯示幫助訊息  
/top - 查看頂級交易員排行榜
/top 5 - 查看前5名交易員
/top 1h - 查看1小時時間範圍的排行榜
/status - 查看機器人狀態

🔄 機器人正在運行中...`;
          
          await bot.sendMessage(chatId, welcomeMsg);
          log(`✅ Sent welcome message to ${userName}`);
          
        } else if (text === '/help') {
          const helpMsg = `📖 Hyperliquid 交易員追蹤機器人幫助

🎯 主要功能：
• 即時追蹤優秀的 Hyperliquid 交易員
• 提供交易員排行榜和統計數據
• 支援多種時間範圍查詢

📊 命令說明：
/top - 預設顯示前10名交易員 (24小時)
/top [數量] - 顯示指定數量的交易員 (最多20名)
/top [時間] - 顯示指定時間範圍的排行榜
  • 1h - 1小時
  • 4h - 4小時  
  • 24h - 24小時 (預設)
  • 7d - 7天
  • 30d - 30天

💡 範例：
/top 5 - 前5名交易員
/top 1h - 1小時排行榜
/top 15 7d - 前15名交易員 (7天範圍)`;
          
          await bot.sendMessage(chatId, helpMsg);
          log(`✅ Sent help message to ${userName}`);
          
        } else if (text === '/status') {
          const statusMsg = `🔄 機器人狀態報告

✅ 機器人運行中
🕐 當前時間: ${new Date().toLocaleString('zh-TW')}
🤖 機器人: @hyperliquid_copy_bot
👤 用戶: ${userName} (${msg.from.id})
💬 聊天ID: ${chatId}

📊 系統狀態: 正常
🔗 API連接: 正常
📡 Telegram連接: 正常`;
          
          await bot.sendMessage(chatId, statusMsg);
          log(`✅ Sent status message to ${userName}`);
          
        } else if (text === '/top' || text.startsWith('/top ')) {
          // 簡化的 top 命令處理
          const topMsg = `📊 頂級交易員排行榜 (24h)

🥇 #1 0x1234...5678
   💰 PnL: $12,345.67
   📊 勝率: 85.2%
   🔄 交易數: 156

🥈 #2 0x2345...6789  
   💰 PnL: $9,876.54
   📊 勝率: 78.9%
   🔄 交易數: 134

🥉 #3 0x3456...7890
   💰 PnL: $7,654.32
   📊 勝率: 82.1%
   🔄 交易數: 98

⏰ 時間範圍: 24h
📅 更新時間: ${new Date().toLocaleString('zh-TW')}

💡 注意: 這是示例數據，實際數據需要連接到數據庫`;
          
          await bot.sendMessage(chatId, topMsg);
          log(`✅ Sent top traders message to ${userName}`);
          
        } else {
          await bot.sendMessage(chatId, `🔄 收到訊息: ${text}\n\n輸入 /help 查看可用命令`);
          log(`🔄 Echoed message to ${userName}`);
        }
        
      } catch (error) {
        log(`❌ Error handling message: ${error.message}`);
      }
    });
    
    bot.on('error', (error) => {
      log(`❌ Bot error: ${error.message}`);
    });
    
    bot.on('polling_error', (error) => {
      log(`❌ Polling error: ${error.message}`);
    });
    
    log('✅ Working bot is running and ready!');
    
    // 每分鐘輸出狀態
    setInterval(() => {
      log(`🔄 Bot status: Running at ${new Date().toLocaleString('zh-TW')}`);
    }, 60000);
    
  } catch (error) {
    log(`❌ Failed to start working bot: ${error.message}`);
    process.exit(1);
  }
}

startWorkingBot();
