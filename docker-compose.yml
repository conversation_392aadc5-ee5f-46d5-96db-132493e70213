version: '3.8'

services:
  # 主應用服務
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - DATABASE_PATH=/app/data/hyperliquid.db
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_ENABLED=true
    volumes:
      - app_data:/app/data
      - ./logs:/app/logs
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - hyperliquid_network

  # Redis 緩存服務
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    restart: unless-stopped
    networks:
      - hyperliquid_network

  # Nginx 反向代理 (可選)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - app
    restart: unless-stopped
    networks:
      - hyperliquid_network

volumes:
  app_data:
    driver: local
  redis_data:
    driver: local

networks:
  hyperliquid_network:
    driver: bridge
