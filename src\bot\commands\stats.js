import { timeframes } from '../config/index.js';
import logger from '../../utils/logger.js';
import database from '../../models/database.js';

class StatsCommands {
  constructor(bot) {
    this.bot = bot;
  }

  async stats(msg, args) {
    const chatId = msg.chat.id;

    try {
      // 解析時間範圍參數
      let timeframe = '24h';
      if (args.length > 0 && timeframes[args[0]]) {
        timeframe = args[0];
      }

      // 獲取統計數據
      const stats = await this.getTradeStats(timeframe);

      if (!stats) {
        await this.bot.bot.sendMessage(chatId, 
          '📊 暫無統計數據，請稍後再試。'
        );
        return;
      }

      // 格式化統計消息
      const message = this.formatStatsMessage(stats, timeframe);

      // 創建時間範圍切換鍵盤
      const keyboard = this.createStatsKeyboard(timeframe);

      await this.bot.bot.sendMessage(chatId, message, {
        reply_markup: keyboard,
        parse_mode: 'HTML'
      });

    } catch (error) {
      logger.error('Error in stats command:', error);
      await this.bot.sendErrorMessage(chatId, '獲取統計數據失敗。');
    }
  }

  async positions(msg, args) {
    const chatId = msg.chat.id;

    try {
      // 解析幣種參數
      let coin = null;
      if (args.length > 0) {
        coin = args[0].toUpperCase();
      }

      // 獲取持倉統計
      const positionStats = await this.getPositionStats(coin);

      if (!positionStats || positionStats.length === 0) {
        const message = coin 
          ? `📊 暫無 ${coin} 的持倉數據。`
          : '📊 暫無持倉數據。';
        await this.bot.bot.sendMessage(chatId, message);
        return;
      }

      // 格式化持倉消息
      const message = this.formatPositionMessage(positionStats, coin);

      // 創建幣種切換鍵盤
      const keyboard = this.createPositionKeyboard(coin);

      await this.bot.bot.sendMessage(chatId, message, {
        reply_markup: keyboard,
        parse_mode: 'HTML'
      });

    } catch (error) {
      logger.error('Error in positions command:', error);
      await this.bot.sendErrorMessage(chatId, '獲取持倉統計失敗。');
    }
  }

  async volume(msg, args) {
    const chatId = msg.chat.id;

    try {
      // 解析時間範圍參數
      let timeframe = '24h';
      if (args.length > 0 && timeframes[args[0]]) {
        timeframe = args[0];
      }

      // 獲取交易量統計
      const volumeStats = await this.getVolumeStats(timeframe);

      if (!volumeStats) {
        await this.bot.bot.sendMessage(chatId, 
          '📊 暫無交易量數據，請稍後再試。'
        );
        return;
      }

      // 格式化交易量消息
      const message = this.formatVolumeMessage(volumeStats, timeframe);

      // 創建時間範圍切換鍵盤
      const keyboard = this.createVolumeKeyboard(timeframe);

      await this.bot.bot.sendMessage(chatId, message, {
        reply_markup: keyboard,
        parse_mode: 'HTML'
      });

    } catch (error) {
      logger.error('Error in volume command:', error);
      await this.bot.sendErrorMessage(chatId, '獲取交易量統計失敗。');
    }
  }

  async performance(msg, args) {
    const chatId = msg.chat.id;

    try {
      // 解析時間範圍參數
      let timeframe = '24h';
      if (args.length > 0 && timeframes[args[0]]) {
        timeframe = args[0];
      }

      // 獲取績效統計
      const performanceStats = await this.getPerformanceStats(timeframe);

      if (!performanceStats) {
        await this.bot.bot.sendMessage(chatId, 
          '📊 暫無績效數據，請稍後再試。'
        );
        return;
      }

      // 格式化績效消息
      const message = this.formatPerformanceMessage(performanceStats, timeframe);

      // 創建時間範圍切換鍵盤
      const keyboard = this.createPerformanceKeyboard(timeframe);

      await this.bot.bot.sendMessage(chatId, message, {
        reply_markup: keyboard,
        parse_mode: 'HTML'
      });

    } catch (error) {
      logger.error('Error in performance command:', error);
      await this.bot.sendErrorMessage(chatId, '獲取績效統計失敗。');
    }
  }

  async getTradeStats(timeframe) {
    try {
      const timeframeSeconds = timeframes[timeframe]?.seconds || 86400;
      const startTime = Date.now() - (timeframeSeconds * 1000);

      const stats = await database.get(`
        SELECT 
          COUNT(DISTINCT trader_address) as active_traders,
          COUNT(*) as total_trades,
          SUM(size * price) as total_volume,
          AVG(CASE WHEN pnl > 0 THEN 1.0 ELSE 0.0 END) as avg_win_rate,
          SUM(pnl) as total_pnl,
          AVG(pnl) as avg_pnl
        FROM trades 
        WHERE timestamp > ?
      `, [startTime]);

      // 獲取熱門幣種
      const topCoins = await database.all(`
        SELECT 
          coin,
          COUNT(*) as trade_count,
          SUM(size * price) as volume,
          SUM(CASE WHEN side = 'long' THEN 1 ELSE 0 END) as long_count,
          SUM(CASE WHEN side = 'short' THEN 1 ELSE 0 END) as short_count
        FROM trades 
        WHERE timestamp > ?
        GROUP BY coin
        ORDER BY volume DESC
        LIMIT 5
      `, [startTime]);

      return {
        ...stats,
        topCoins
      };
    } catch (error) {
      logger.error('Error getting trade stats:', error);
      throw error;
    }
  }

  async getPositionStats(coin) {
    try {
      let query = `
        SELECT 
          coin,
          side,
          COUNT(*) as position_count,
          SUM(size) as total_size,
          AVG(size) as avg_size,
          SUM(CASE WHEN pnl > 0 THEN 1 ELSE 0 END) as profitable_positions
        FROM positions 
        WHERE is_open = 1
      `;
      
      const params = [];
      
      if (coin) {
        query += ' AND coin = ?';
        params.push(coin);
      }
      
      query += ' GROUP BY coin, side ORDER BY total_size DESC';

      return await database.all(query, params);
    } catch (error) {
      logger.error('Error getting position stats:', error);
      throw error;
    }
  }

  async getVolumeStats(timeframe) {
    try {
      const timeframeSeconds = timeframes[timeframe]?.seconds || 86400;
      const startTime = Date.now() - (timeframeSeconds * 1000);

      const volumeStats = await database.get(`
        SELECT 
          SUM(size * price) as total_volume,
          COUNT(*) as total_trades,
          AVG(size * price) as avg_trade_size,
          COUNT(DISTINCT trader_address) as unique_traders
        FROM trades 
        WHERE timestamp > ?
      `, [startTime]);

      // 按小時分組的交易量
      const hourlyVolume = await database.all(`
        SELECT 
          strftime('%H', datetime(timestamp/1000, 'unixepoch')) as hour,
          SUM(size * price) as volume,
          COUNT(*) as trades
        FROM trades 
        WHERE timestamp > ?
        GROUP BY hour
        ORDER BY hour
      `, [startTime]);

      return {
        ...volumeStats,
        hourlyVolume
      };
    } catch (error) {
      logger.error('Error getting volume stats:', error);
      throw error;
    }
  }

  async getPerformanceStats(timeframe) {
    try {
      const timeframeSeconds = timeframes[timeframe]?.seconds || 86400;
      const startTime = Date.now() - (timeframeSeconds * 1000);

      const performanceStats = await database.get(`
        SELECT 
          COUNT(DISTINCT trader_address) as total_traders,
          SUM(CASE WHEN pnl > 0 THEN 1 ELSE 0 END) as profitable_traders,
          AVG(pnl) as avg_pnl,
          MAX(pnl) as max_pnl,
          MIN(pnl) as min_pnl,
          SUM(pnl) as total_pnl
        FROM (
          SELECT 
            trader_address,
            SUM(pnl) as pnl
          FROM trades 
          WHERE timestamp > ?
          GROUP BY trader_address
        )
      `, [startTime]);

      // 績效分布
      const performanceDistribution = await database.all(`
        SELECT 
          CASE 
            WHEN pnl_sum > 1000 THEN 'Excellent (>$1000)'
            WHEN pnl_sum > 100 THEN 'Good ($100-$1000)'
            WHEN pnl_sum > 0 THEN 'Profitable ($0-$100)'
            WHEN pnl_sum > -100 THEN 'Small Loss ($0 to -$100)'
            ELSE 'Large Loss (<-$100)'
          END as performance_tier,
          COUNT(*) as trader_count
        FROM (
          SELECT 
            trader_address,
            SUM(pnl) as pnl_sum
          FROM trades 
          WHERE timestamp > ?
          GROUP BY trader_address
        )
        GROUP BY performance_tier
      `, [startTime]);

      return {
        ...performanceStats,
        performanceDistribution
      };
    } catch (error) {
      logger.error('Error getting performance stats:', error);
      throw error;
    }
  }

  formatStatsMessage(stats, timeframe) {
    const timeLabel = timeframes[timeframe]?.label || '24小時';
    
    let message = `📈 交易統計 (${timeLabel})\n\n`;
    
    message += `👥 活躍交易員: ${(stats.active_traders || 0).toLocaleString()}\n`;
    message += `📊 總交易數: ${(stats.total_trades || 0).toLocaleString()}\n`;
    message += `💰 總交易量: $${(stats.total_volume || 0).toLocaleString()}\n`;
    message += `📈 平均勝率: ${((stats.avg_win_rate || 0) * 100).toFixed(1)}%\n`;
    message += `💵 總收益: ${(stats.total_pnl || 0) >= 0 ? '+' : ''}${(stats.total_pnl || 0).toFixed(2)} USDC\n\n`;

    if (stats.topCoins && stats.topCoins.length > 0) {
      message += `🔥 熱門幣種:\n`;
      stats.topCoins.forEach((coin, index) => {
        const longPercentage = coin.long_count / (coin.long_count + coin.short_count) * 100;
        const shortPercentage = 100 - longPercentage;
        message += `${index + 1}. ${coin.coin}: `;
        message += `🟢 ${longPercentage.toFixed(0)}% 做多 `;
        message += `🔴 ${shortPercentage.toFixed(0)}% 做空\n`;
      });
    }

    return message;
  }

  formatPositionMessage(positionStats, coin) {
    let message = coin 
      ? `📊 ${coin} 持倉統計\n\n`
      : `📊 持倉統計\n\n`;

    positionStats.forEach(stat => {
      const side = stat.side === 'long' ? '🟢 做多' : '🔴 做空';
      const profitableRate = (stat.profitable_positions / stat.position_count * 100).toFixed(1);
      
      message += `${stat.coin} ${side}:\n`;
      message += `• 持倉數量: ${stat.position_count}\n`;
      message += `• 總倉位: $${stat.total_size.toLocaleString()}\n`;
      message += `• 平均倉位: $${stat.avg_size.toLocaleString()}\n`;
      message += `• 盈利比例: ${profitableRate}%\n\n`;
    });

    return message;
  }

  formatVolumeMessage(volumeStats, timeframe) {
    const timeLabel = timeframes[timeframe]?.label || '24小時';
    
    let message = `💰 交易量統計 (${timeLabel})\n\n`;
    
    message += `📊 總交易量: $${(volumeStats.total_volume || 0).toLocaleString()}\n`;
    message += `📈 總交易數: ${(volumeStats.total_trades || 0).toLocaleString()}\n`;
    message += `💵 平均交易額: $${(volumeStats.avg_trade_size || 0).toLocaleString()}\n`;
    message += `👥 參與交易員: ${(volumeStats.unique_traders || 0).toLocaleString()}\n\n`;

    if (volumeStats.hourlyVolume && volumeStats.hourlyVolume.length > 0) {
      message += `⏰ 小時分布:\n`;
      volumeStats.hourlyVolume.forEach(hour => {
        message += `${hour.hour}:00 - $${hour.volume.toLocaleString()} (${hour.trades} 筆)\n`;
      });
    }

    return message;
  }

  formatPerformanceMessage(performanceStats, timeframe) {
    const timeLabel = timeframes[timeframe]?.label || '24小時';
    
    let message = `🎯 績效統計 (${timeLabel})\n\n`;
    
    const profitableRate = (performanceStats.profitable_traders / performanceStats.total_traders * 100).toFixed(1);
    
    message += `👥 總交易員: ${performanceStats.total_traders}\n`;
    message += `✅ 盈利交易員: ${performanceStats.profitable_traders} (${profitableRate}%)\n`;
    message += `💰 平均收益: ${(performanceStats.avg_pnl || 0).toFixed(2)} USDC\n`;
    message += `🚀 最高收益: ${(performanceStats.max_pnl || 0).toFixed(2)} USDC\n`;
    message += `📉 最大虧損: ${(performanceStats.min_pnl || 0).toFixed(2)} USDC\n\n`;

    if (performanceStats.performanceDistribution) {
      message += `📊 績效分布:\n`;
      performanceStats.performanceDistribution.forEach(dist => {
        message += `• ${dist.performance_tier}: ${dist.trader_count} 人\n`;
      });
    }

    return message;
  }

  createStatsKeyboard(timeframe) {
    const keyboard = { inline_keyboard: [] };

    // 時間範圍切換
    const timeRow = [];
    Object.keys(timeframes).forEach(tf => {
      const label = tf === timeframe ? `• ${tf} •` : tf;
      timeRow.push({
        text: label,
        callback_data: `stats:${tf}`
      });
    });
    keyboard.inline_keyboard.push(timeRow);

    // 其他統計選項
    keyboard.inline_keyboard.push([
      { text: '📊 持倉統計', callback_data: 'positions:all' },
      { text: '💰 交易量', callback_data: `volume:${timeframe}` }
    ]);

    keyboard.inline_keyboard.push([
      { text: '🎯 績效分析', callback_data: `performance:${timeframe}` },
      { text: '👥 交易員排行', callback_data: `top_traders:${timeframe}:1` }
    ]);

    return keyboard;
  }

  createPositionKeyboard(coin) {
    const keyboard = { inline_keyboard: [] };

    // 熱門幣種快捷按鈕
    const coinRow = [];
    ['ETH', 'BTC', 'SOL', 'AVAX'].forEach(c => {
      const label = c === coin ? `• ${c} •` : c;
      coinRow.push({
        text: label,
        callback_data: `positions:${c}`
      });
    });
    keyboard.inline_keyboard.push(coinRow);

    keyboard.inline_keyboard.push([
      { text: '📊 全部持倉', callback_data: 'positions:all' },
      { text: '📈 返回統計', callback_data: 'stats:24h' }
    ]);

    return keyboard;
  }

  createVolumeKeyboard(timeframe) {
    return this.createStatsKeyboard(timeframe);
  }

  createPerformanceKeyboard(timeframe) {
    return this.createStatsKeyboard(timeframe);
  }
}

export default StatsCommands;
