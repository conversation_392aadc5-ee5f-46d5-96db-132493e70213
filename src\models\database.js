import sqlite3 from 'sqlite3';
import { config } from '../config/index.js';
import logger from '../utils/logger.js';
import fs from 'fs';
import path from 'path';

class Database {
  constructor() {
    this.db = null;
    this.isInitialized = false;
  }

  async initialize() {
    if (this.isInitialized) return;

    try {
      // 確保數據目錄存在
      const dbDir = path.dirname(config.database.path);
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true });
      }

      // 創建數據庫連接
      this.db = new sqlite3.Database(config.database.path, (err) => {
        if (err) {
          logger.error('Error opening database:', err);
          throw err;
        }
        logger.info(`Database connected: ${config.database.path}`);
      });

      // 啟用外鍵約束
      await this.run('PRAGMA foreign_keys = ON');
      
      // 創建表結構
      await this.createTables();
      
      this.isInitialized = true;
      logger.info('Database initialized successfully');
    } catch (error) {
      logger.error('Error initializing database:', error);
      throw error;
    }
  }

  async createTables() {
    const tables = [
      // 交易員表
      `CREATE TABLE IF NOT EXISTS traders (
        address TEXT PRIMARY KEY,
        first_seen INTEGER NOT NULL,
        last_updated INTEGER NOT NULL,
        total_trades INTEGER DEFAULT 0,
        total_volume REAL DEFAULT 0,
        total_pnl REAL DEFAULT 0,
        win_rate REAL DEFAULT 0,
        sharpe_ratio REAL DEFAULT 0,
        max_drawdown REAL DEFAULT 0,
        avg_position_size REAL DEFAULT 0,
        risk_score REAL DEFAULT 0,
        performance_score REAL DEFAULT 0,
        is_active BOOLEAN DEFAULT 1,
        created_at INTEGER DEFAULT (strftime('%s', 'now') * 1000),
        updated_at INTEGER DEFAULT (strftime('%s', 'now') * 1000)
      )`,

      // 交易記錄表
      `CREATE TABLE IF NOT EXISTS trades (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        trader_address TEXT NOT NULL,
        coin TEXT NOT NULL,
        side TEXT NOT NULL,
        size REAL NOT NULL,
        price REAL NOT NULL,
        timestamp INTEGER NOT NULL,
        pnl REAL DEFAULT 0,
        fee REAL DEFAULT 0,
        hash TEXT UNIQUE,
        oid INTEGER,
        tid INTEGER,
        crossed BOOLEAN DEFAULT 0,
        direction TEXT,
        start_position REAL DEFAULT 0,
        created_at INTEGER DEFAULT (strftime('%s', 'now') * 1000),
        FOREIGN KEY (trader_address) REFERENCES traders (address)
      )`,

      // 持倉表
      `CREATE TABLE IF NOT EXISTS positions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        trader_address TEXT NOT NULL,
        coin TEXT NOT NULL,
        size REAL NOT NULL,
        entry_price REAL NOT NULL,
        current_price REAL,
        unrealized_pnl REAL DEFAULT 0,
        timestamp INTEGER NOT NULL,
        is_open BOOLEAN DEFAULT 1,
        created_at INTEGER DEFAULT (strftime('%s', 'now') * 1000),
        updated_at INTEGER DEFAULT (strftime('%s', 'now') * 1000),
        FOREIGN KEY (trader_address) REFERENCES traders (address),
        UNIQUE(trader_address, coin)
      )`,

      // 績效統計表
      `CREATE TABLE IF NOT EXISTS performance_stats (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        trader_address TEXT NOT NULL,
        timeframe TEXT NOT NULL,
        start_time INTEGER NOT NULL,
        end_time INTEGER NOT NULL,
        total_trades INTEGER DEFAULT 0,
        winning_trades INTEGER DEFAULT 0,
        total_volume REAL DEFAULT 0,
        total_pnl REAL DEFAULT 0,
        max_drawdown REAL DEFAULT 0,
        sharpe_ratio REAL DEFAULT 0,
        win_rate REAL DEFAULT 0,
        avg_trade_size REAL DEFAULT 0,
        created_at INTEGER DEFAULT (strftime('%s', 'now') * 1000),
        FOREIGN KEY (trader_address) REFERENCES traders (address),
        UNIQUE(trader_address, timeframe, start_time)
      )`,

      // 實時統計表
      `CREATE TABLE IF NOT EXISTS real_time_stats (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        timeframe TEXT NOT NULL,
        coin TEXT,
        total_traders INTEGER DEFAULT 0,
        active_traders INTEGER DEFAULT 0,
        long_positions INTEGER DEFAULT 0,
        short_positions INTEGER DEFAULT 0,
        long_percentage REAL DEFAULT 0,
        short_percentage REAL DEFAULT 0,
        avg_position_size REAL DEFAULT 0,
        total_volume REAL DEFAULT 0,
        timestamp INTEGER NOT NULL,
        created_at INTEGER DEFAULT (strftime('%s', 'now') * 1000),
        UNIQUE(timeframe, coin, timestamp)
      )`,

      // 交易員排名表
      `CREATE TABLE IF NOT EXISTS trader_rankings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        trader_address TEXT NOT NULL,
        rank_position INTEGER NOT NULL,
        score REAL NOT NULL,
        timeframe TEXT NOT NULL,
        timestamp INTEGER NOT NULL,
        created_at INTEGER DEFAULT (strftime('%s', 'now') * 1000),
        FOREIGN KEY (trader_address) REFERENCES traders (address),
        UNIQUE(trader_address, timeframe, timestamp)
      )`
    ];

    for (const table of tables) {
      await this.run(table);
    }

    // 創建索引
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_trades_trader_timestamp ON trades(trader_address, timestamp)',
      'CREATE INDEX IF NOT EXISTS idx_trades_coin_timestamp ON trades(coin, timestamp)',
      'CREATE INDEX IF NOT EXISTS idx_positions_trader_coin ON positions(trader_address, coin)',
      'CREATE INDEX IF NOT EXISTS idx_performance_trader_timeframe ON performance_stats(trader_address, timeframe)',
      'CREATE INDEX IF NOT EXISTS idx_rankings_timeframe_rank ON trader_rankings(timeframe, rank_position)',
      'CREATE INDEX IF NOT EXISTS idx_real_time_stats_timeframe_timestamp ON real_time_stats(timeframe, timestamp)'
    ];

    for (const index of indexes) {
      await this.run(index);
    }
  }

  // 包裝 sqlite3 方法為 Promise
  run(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function(err) {
        if (err) {
          logger.error('Database run error:', err);
          reject(err);
        } else {
          resolve({ lastID: this.lastID, changes: this.changes });
        }
      });
    });
  }

  get(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.get(sql, params, (err, row) => {
        if (err) {
          logger.error('Database get error:', err);
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  all(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) {
          logger.error('Database all error:', err);
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // 事務處理
  async transaction(callback) {
    await this.run('BEGIN TRANSACTION');
    try {
      await callback();
      await this.run('COMMIT');
    } catch (error) {
      await this.run('ROLLBACK');
      throw error;
    }
  }

  // 關閉數據庫連接
  close() {
    return new Promise((resolve, reject) => {
      if (this.db) {
        this.db.close((err) => {
          if (err) {
            logger.error('Error closing database:', err);
            reject(err);
          } else {
            logger.info('Database connection closed');
            resolve();
          }
        });
      } else {
        resolve();
      }
    });
  }
}

// 創建單例實例
const database = new Database();

export default database;
