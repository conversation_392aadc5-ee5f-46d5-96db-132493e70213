import { botConfig } from '../config/index.js';
import logger from '../../utils/logger.js';

// 簡單的內存速率限制器
class RateLimiter {
  constructor() {
    this.requests = new Map(); // userId -> { count, resetTime }
  }

  isAllowed(userId) {
    const now = Date.now();
    const userRequests = this.requests.get(userId);

    if (!userRequests) {
      // 首次請求
      this.requests.set(userId, {
        count: 1,
        resetTime: now + botConfig.rateLimit.windowMs
      });
      return true;
    }

    if (now > userRequests.resetTime) {
      // 重置計數器
      this.requests.set(userId, {
        count: 1,
        resetTime: now + botConfig.rateLimit.windowMs
      });
      return true;
    }

    if (userRequests.count >= botConfig.rateLimit.maxRequests) {
      // 超過限制
      return false;
    }

    // 增加計數
    userRequests.count++;
    return true;
  }

  getRemainingTime(userId) {
    const userRequests = this.requests.get(userId);
    if (!userRequests) return 0;
    
    const now = Date.now();
    return Math.max(0, userRequests.resetTime - now);
  }

  // 清理過期記錄
  cleanup() {
    const now = Date.now();
    for (const [userId, data] of this.requests.entries()) {
      if (now > data.resetTime) {
        this.requests.delete(userId);
      }
    }
  }
}

const rateLimiter = new RateLimiter();

// 定期清理過期記錄
setInterval(() => {
  rateLimiter.cleanup();
}, 60000); // 每分鐘清理一次

// 速率限制中間件
async function rateLimitMiddleware(msg, bot) {
  try {
    const userId = msg.from.id;
    
    if (!rateLimiter.isAllowed(userId)) {
      const remainingTime = rateLimiter.getRemainingTime(userId);
      const remainingMinutes = Math.ceil(remainingTime / 60000);
      
      await bot.bot.sendMessage(msg.chat.id, 
        `⏰ 您的請求過於頻繁，請等待 ${remainingMinutes} 分鐘後再試。`
      );
      
      logger.warn(`Rate limit exceeded for user ${userId}`);
      return false;
    }

    return true;
  } catch (error) {
    logger.error('Rate limit middleware error:', error);
    return true; // 出錯時允許通過
  }
}

export default rateLimitMiddleware;
