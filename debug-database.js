import database from './src/models/database.js';

async function debugDatabase() {
  try {
    console.log('🔍 調試數據庫數據...');
    
    await database.initialize();
    
    // 檢查 traders 表的數據
    console.log('\n📊 檢查 traders 表的數據結構和內容：');
    const traders = await database.all(`
      SELECT 
        address,
        total_trades,
        win_rate,
        total_pnl,
        total_volume,
        performance_score
      FROM traders 
      WHERE is_active = 1 AND total_trades > 0
      ORDER BY total_pnl DESC 
      LIMIT 5
    `);
    
    console.log(`找到 ${traders.length} 名交易員：`);
    traders.forEach((trader, index) => {
      console.log(`\n${index + 1}. 地址: ${trader.address}`);
      console.log(`   total_trades: ${trader.total_trades} (類型: ${typeof trader.total_trades})`);
      console.log(`   win_rate: ${trader.win_rate} (類型: ${typeof trader.win_rate})`);
      console.log(`   total_pnl: ${trader.total_pnl} (類型: ${typeof trader.total_pnl})`);
      console.log(`   total_volume: ${trader.total_volume} (類型: ${typeof trader.total_volume})`);
      console.log(`   performance_score: ${trader.performance_score}`);
    });
    
    // 檢查 trader_rankings 表
    console.log('\n📈 檢查 trader_rankings 表：');
    const rankings = await database.all(`
      SELECT 
        trader_address,
        rank_position,
        score,
        timeframe,
        timestamp
      FROM trader_rankings 
      ORDER BY timestamp DESC, rank_position ASC
      LIMIT 5
    `);
    
    console.log(`找到 ${rankings.length} 條排名記錄：`);
    rankings.forEach((rank, index) => {
      console.log(`\n${index + 1}. 地址: ${rank.trader_address}`);
      console.log(`   rank_position: ${rank.rank_position}`);
      console.log(`   score: ${rank.score}`);
      console.log(`   timeframe: ${rank.timeframe}`);
      console.log(`   timestamp: ${rank.timestamp} (${new Date(rank.timestamp).toLocaleString()})`);
    });
    
    // 檢查 trades 表來計算實際的勝率和 PnL
    console.log('\n💹 檢查 trades 表來計算勝率：');
    if (traders.length > 0) {
      const sampleTrader = traders[0];
      console.log(`\n分析交易員: ${sampleTrader.address}`);
      
      const allTrades = await database.all(`
        SELECT 
          pnl,
          size,
          price,
          timestamp
        FROM trades 
        WHERE trader_address = ?
        ORDER BY timestamp DESC
        LIMIT 10
      `, [sampleTrader.address]);
      
      console.log(`該交易員的最近 ${allTrades.length} 筆交易：`);
      let winningTrades = 0;
      let totalPnl = 0;
      
      allTrades.forEach((trade, index) => {
        console.log(`   ${index + 1}. PnL: ${trade.pnl}, Size: ${trade.size}, Price: ${trade.price}`);
        if (trade.pnl > 0) winningTrades++;
        totalPnl += trade.pnl || 0;
      });
      
      const calculatedWinRate = allTrades.length > 0 ? (winningTrades / allTrades.length) : 0;
      console.log(`\n計算結果：`);
      console.log(`   勝率: ${(calculatedWinRate * 100).toFixed(1)}% (${winningTrades}/${allTrades.length})`);
      console.log(`   總 PnL: ${totalPnl.toFixed(2)}`);
    }
    
    // 檢查數據庫表結構
    console.log('\n🏗️ 檢查 traders 表結構：');
    const tableInfo = await database.all("PRAGMA table_info(traders)");
    tableInfo.forEach(col => {
      console.log(`   ${col.name}: ${col.type} (nullable: ${col.notnull === 0})`);
    });
    
  } catch (error) {
    console.error('❌ 調試失敗:', error.message);
    console.error(error.stack);
  }
}

debugDatabase();
