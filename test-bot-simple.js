import dotenv from 'dotenv';
import TelegramBot from 'node-telegram-bot-api';

dotenv.config();

console.log('🚀 Starting simple bot test...');

async function testSimpleBot() {
  console.log('🤖 Testing Simple Telegram Bot...');
  
  try {
    // 檢查 Bot Token
    if (!process.env.TELEGRAM_BOT_TOKEN) {
      console.warn('❌ TELEGRAM_BOT_TOKEN not set');
      return;
    }

    console.log('✅ Bot token found');

    // 創建 Bot 實例
    const bot = new TelegramBot(process.env.TELEGRAM_BOT_TOKEN, { 
      polling: {
        interval: 1000,
        autoStart: true,
        params: {
          timeout: 10
        }
      }
    });

    console.log('🎉 Telegram Bot created successfully!');

    // 測試 Bot 信息
    const me = await bot.getMe();
    console.log('🤖 Bot info:', {
      id: me.id,
      username: me.username,
      first_name: me.first_name
    });

    // 設置指令處理
    bot.onText(/\/start/, async (msg) => {
      const chatId = msg.chat.id;
      const username = msg.from.username || msg.from.first_name;
      
      console.log('📨 Received /start from:', username);
      
      await bot.sendMessage(chatId, 
        '🎉 歡迎使用 Hyperliquid 交易員追蹤機器人！\n\n' +
        '🚀 機器人已成功啟動並運行正常。\n\n' +
        '可用指令:\n' +
        '• /start - 顯示歡迎消息\n' +
        '• /help - 顯示幫助信息\n' +
        '• /ping - 測試機器人響應\n' +
        '• /status - 查看系統狀態'
      );
    });

    bot.onText(/\/help/, async (msg) => {
      const chatId = msg.chat.id;
      console.log('📨 Received /help from:', msg.from.username || msg.from.first_name);
      
      await bot.sendMessage(chatId, 
        '📚 Hyperliquid 交易員追蹤機器人\n\n' +
        '🔧 基本指令:\n' +
        '• /start - 開始使用\n' +
        '• /help - 顯示幫助\n' +
        '• /ping - 測試響應\n' +
        '• /status - 系統狀態\n\n' +
        '💡 完整功能開發中...'
      );
    });

    bot.onText(/\/ping/, async (msg) => {
      const chatId = msg.chat.id;
      console.log('📨 Received /ping from:', msg.from.username || msg.from.first_name);
      
      const start = Date.now();
      await bot.sendMessage(chatId, 
        '🏓 Pong!\n\n' +
        `⚡ 響應時間: ${Date.now() - start}ms\n` +
        `🤖 狀態: 正常運行\n` +
        `📅 時間: ${new Date().toLocaleString()}`
      );
    });

    bot.onText(/\/status/, async (msg) => {
      const chatId = msg.chat.id;
      console.log('📨 Received /status from:', msg.from.username || msg.from.first_name);
      
      await bot.sendMessage(chatId, 
        '📊 系統狀態\n\n' +
        `🤖 機器人: 運行中\n` +
        `📡 連接: 正常\n` +
        `🕐 運行時間: ${process.uptime().toFixed(0)}秒\n` +
        `📅 當前時間: ${new Date().toLocaleString()}`
      );
    });

    // 處理所有其他消息
    bot.on('message', async (msg) => {
      if (!msg.text || msg.text.startsWith('/')) return;
      
      const chatId = msg.chat.id;
      console.log('📨 Received message from:', msg.from.username || msg.from.first_name, ':', msg.text);
      
      await bot.sendMessage(chatId, 
        '🤖 收到您的消息！\n\n' +
        `您說: "${msg.text}"\n\n` +
        '請使用 /help 查看可用指令。'
      );
    });

    console.log('🚀 Bot is now running and ready!');
    console.log('💬 Send /start to test the bot');
    console.log('🛑 Press Ctrl+C to stop');

    // 錯誤處理
    bot.on('error', (error) => {
      console.error('❌ Bot error:', error.message);
    });

    bot.on('polling_error', (error) => {
      console.error('❌ Polling error:', error.message);
      if (error.message.includes('409')) {
        console.log('💡 Tip: Make sure no other bot instances are running');
      }
    });

    // 優雅關閉
    process.on('SIGINT', () => {
      console.log('\n🛑 Shutting down...');
      bot.stopPolling();
      console.log('✅ Bot stopped');
      process.exit(0);
    });

  } catch (error) {
    console.error('❌ Bot test failed:', error.message);
    console.error('Stack:', error.stack);
    process.exit(1);
  }
}

testSimpleBot().catch(error => {
  console.error('❌ Unhandled error:', error.message);
  process.exit(1);
});
