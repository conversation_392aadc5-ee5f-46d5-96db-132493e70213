import dotenv from 'dotenv';
import TelegramBot from 'node-telegram-bot-api';

dotenv.config();

async function clearBotState() {
  console.log('🧹 Clearing Telegram Bot state...');
  
  try {
    if (!process.env.TELEGRAM_BOT_TOKEN) {
      console.error('❌ TELEGRAM_BOT_TOKEN not found in .env file');
      return;
    }

    // 創建 Bot 實例但不啟動 polling
    const bot = new TelegramBot(process.env.TELEGRAM_BOT_TOKEN, { 
      polling: false 
    });

    console.log('✅ Bot instance created (no polling)');

    // 獲取 Bot 信息
    try {
      const me = await bot.getMe();
      console.log('🤖 Bot info:', {
        id: me.id,
        username: me.username,
        first_name: me.first_name
      });
    } catch (error) {
      console.error('❌ Failed to get bot info:', error.message);
      return;
    }

    // 刪除 webhook (如果有設置的話)
    try {
      const result = await bot.deleteWebHook();
      console.log('🗑️ Webhook deleted:', result);
    } catch (error) {
      console.log('ℹ️ No webhook to delete or error:', error.message);
    }

    // 獲取待處理的更新並清空
    try {
      const updates = await bot.getUpdates({ timeout: 1 });
      console.log(`📨 Found ${updates.length} pending updates`);
      
      if (updates.length > 0) {
        // 獲取最後一個更新的 ID
        const lastUpdateId = updates[updates.length - 1].update_id;
        
        // 確認所有待處理的更新
        await bot.getUpdates({ offset: lastUpdateId + 1, timeout: 1 });
        console.log('✅ All pending updates cleared');
      }
    } catch (error) {
      console.log('ℹ️ Error clearing updates:', error.message);
    }

    console.log('🎉 Bot state cleared successfully!');
    console.log('💡 You can now start a new bot instance without conflicts.');
    
  } catch (error) {
    console.error('❌ Error clearing bot state:', error.message);
    console.error('Stack:', error.stack);
  }
}

clearBotState().then(() => {
  console.log('✅ Bot state clearing completed');
  process.exit(0);
}).catch(error => {
  console.error('❌ Unhandled error:', error.message);
  process.exit(1);
});
