import TelegramBot from 'node-telegram-bot-api';
import dotenv from 'dotenv';

dotenv.config();

async function testBotStatus() {
  console.log('🔍 Testing Telegram Bot Status...');
  
  try {
    const token = process.env.TELEGRAM_BOT_TOKEN;
    
    if (!token) {
      console.error('❌ TELEGRAM_BOT_TOKEN not found in environment');
      return;
    }
    
    console.log('✅ Bot token found');
    
    // 創建 Bot 實例但不啟動 polling
    const bot = new TelegramBot(token, { polling: false });
    
    // 測試 Bot 連接
    const me = await bot.getMe();
    console.log('🤖 Bot Info:');
    console.log(`   ID: ${me.id}`);
    console.log(`   Username: @${me.username}`);
    console.log(`   Name: ${me.first_name}`);
    console.log(`   Can Join Groups: ${me.can_join_groups}`);
    console.log(`   Can Read All Group Messages: ${me.can_read_all_group_messages}`);
    console.log(`   Supports Inline Queries: ${me.supports_inline_queries}`);
    
    // 測試獲取更新
    const updates = await bot.getUpdates({ limit: 5 });
    console.log(`📨 Recent Updates: ${updates.length} messages`);
    
    if (updates.length > 0) {
      console.log('📋 Latest Messages:');
      updates.forEach((update, index) => {
        if (update.message) {
          const msg = update.message;
          console.log(`   ${index + 1}. From: ${msg.from.first_name} (${msg.from.id})`);
          console.log(`      Text: ${msg.text || 'No text'}`);
          console.log(`      Date: ${new Date(msg.date * 1000).toLocaleString()}`);
        }
      });
    }
    
    console.log('✅ Bot is accessible and working!');
    
  } catch (error) {
    console.error('❌ Bot test failed:', error.message);
  }
}

testBotStatus();
