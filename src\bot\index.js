import TelegramBot from 'node-telegram-bot-api';
import { config } from '../config/index.js';
import logger from '../utils/logger.js';
import database from '../models/database.js';
import { botConfig } from './config/index.js';

// 導入指令處理器
import BasicCommands from './commands/basic.js';
import TraderCommands from './commands/traders.js';
import StatsCommands from './commands/stats.js';
import AlertCommands from './commands/alerts.js';

// 導入中間件
import authMiddleware from './middleware/auth.js';
import rateLimitMiddleware from './middleware/rateLimit.js';
import loggingMiddleware from './middleware/logging.js';

class HyperliquidBot {
  constructor() {
    this.bot = null;
    this.isRunning = false;
    this.commands = new Map();
    this.middleware = [];
    
    this.setupCommands();
    this.setupMiddleware();
  }

  async initialize() {
    try {
      if (!botConfig.token) {
        throw new Error('Telegram bot token is required');
      }

      // 創建機器人實例
      this.bot = new TelegramBot(botConfig.token, { 
        polling: true,
        request: {
          agentOptions: {
            keepAlive: true,
            family: 4
          }
        }
      });

      // 設置錯誤處理
      this.bot.on('error', (error) => {
        logger.error('Telegram bot error:', error);
      });

      this.bot.on('polling_error', (error) => {
        logger.error('Telegram polling error:', error);
      });

      // 設置消息處理
      this.setupMessageHandlers();

      // 初始化數據庫表
      await this.initializeBotTables();

      logger.info('Telegram bot initialized successfully');
      this.isRunning = true;

    } catch (error) {
      logger.error('Failed to initialize Telegram bot:', error);
      throw error;
    }
  }

  setupCommands() {
    // 註冊基本指令
    const basicCommands = new BasicCommands(this);
    this.commands.set('start', basicCommands.start.bind(basicCommands));
    this.commands.set('help', basicCommands.help.bind(basicCommands));
    this.commands.set('status', basicCommands.status.bind(basicCommands));

    // 註冊交易員查詢指令
    const traderCommands = new TraderCommands(this);
    this.commands.set('top', traderCommands.top.bind(traderCommands));
    this.commands.set('trader', traderCommands.trader.bind(traderCommands));
    this.commands.set('search', traderCommands.search.bind(traderCommands));

    // 註冊統計查詢指令
    const statsCommands = new StatsCommands(this);
    this.commands.set('stats', statsCommands.stats.bind(statsCommands));
    this.commands.set('positions', statsCommands.positions.bind(statsCommands));
    this.commands.set('volume', statsCommands.volume.bind(statsCommands));
    this.commands.set('performance', statsCommands.performance.bind(statsCommands));

    // 註冊通知管理指令
    const alertCommands = new AlertCommands(this);
    this.commands.set('alerts', alertCommands.alerts.bind(alertCommands));
    this.commands.set('subscribe', alertCommands.subscribe.bind(alertCommands));
    this.commands.set('unsubscribe', alertCommands.unsubscribe.bind(alertCommands));
  }

  setupMiddleware() {
    this.middleware.push(loggingMiddleware);
    this.middleware.push(authMiddleware);
    this.middleware.push(rateLimitMiddleware);
  }

  setupMessageHandlers() {
    // 處理文本消息
    this.bot.on('message', async (msg) => {
      try {
        // 只處理文本消息
        if (msg.text) {
          await this.handleMessage(msg);
        }
      } catch (error) {
        logger.error('Error handling message:', error);
        await this.sendErrorMessage(msg.chat.id, '處理消息時發生錯誤，請稍後重試。');
      }
    });

    // 處理回調查詢
    this.bot.on('callback_query', async (callbackQuery) => {
      try {
        await this.handleCallbackQuery(callbackQuery);
      } catch (error) {
        logger.error('Error handling callback query:', error);
        await this.bot.answerCallbackQuery(callbackQuery.id, {
          text: '處理請求時發生錯誤',
          show_alert: true
        });
      }
    });
  }

  async handleMessage(msg) {
    const chatId = msg.chat.id;
    const text = msg.text;

    // 執行中間件
    for (const middleware of this.middleware) {
      const result = await middleware(msg, this);
      if (!result) {
        return; // 中間件阻止了請求
      }
    }

    // 解析指令
    if (text.startsWith('/')) {
      const [command, ...args] = text.slice(1).split(' ');
      await this.executeCommand(command, args, msg);
    } else {
      // 處理非指令消息
      await this.handleNonCommandMessage(msg);
    }
  }

  async executeCommand(command, args, msg) {
    const chatId = msg.chat.id;
    
    if (this.commands.has(command)) {
      try {
        await this.commands.get(command)(msg, args);
      } catch (error) {
        logger.error(`Error executing command ${command}:`, error);
        await this.sendErrorMessage(chatId, `執行指令 /${command} 時發生錯誤。`);
      }
    } else {
      await this.bot.sendMessage(chatId, 
        `未知指令: /${command}\n使用 /help 查看可用指令。`
      );
    }
  }

  async handleNonCommandMessage(msg) {
    const chatId = msg.chat.id;
    
    // 可以在這裡處理自然語言查詢或其他非指令消息
    await this.bot.sendMessage(chatId, 
      '請使用指令與我互動。輸入 /help 查看可用指令。'
    );
  }

  async handleCallbackQuery(callbackQuery) {
    const data = callbackQuery.data;
    const msg = callbackQuery.message;
    
    // 解析回調數據
    const [action, ...params] = data.split(':');
    
    // 根據動作類型處理回調
    switch (action) {
      case 'top_traders':
        await this.handleTopTradersCallback(msg, params);
        break;
      case 'stats':
        await this.handleStatsCallback(msg, params);
        break;
      case 'trader_detail':
        await this.handleTraderDetailCallback(msg, params);
        break;
      default:
        logger.warn(`Unknown callback action: ${action}`);
    }

    // 回答回調查詢
    await this.bot.answerCallbackQuery(callbackQuery.id);
  }

  async handleTopTradersCallback(msg, params) {
    const [timeframe, page] = params;
    const traderCommands = new (await import('./commands/traders.js')).default(this);
    await traderCommands.top(msg, [timeframe, page]);
  }

  async handleStatsCallback(msg, params) {
    const [timeframe] = params;
    const statsCommands = new (await import('./commands/stats.js')).default(this);
    await statsCommands.stats(msg, [timeframe]);
  }

  async handleTraderDetailCallback(msg, params) {
    const [address] = params;
    const traderCommands = new (await import('./commands/traders.js')).default(this);
    await traderCommands.trader(msg, [address]);
  }

  async sendErrorMessage(chatId, message) {
    try {
      await this.bot.sendMessage(chatId, `❌ ${message}`);
    } catch (error) {
      logger.error('Failed to send error message:', error);
    }
  }

  async initializeBotTables() {
    try {
      // 創建機器人相關的數據庫表
      await database.run(`
        CREATE TABLE IF NOT EXISTS bot_users (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          telegram_id TEXT UNIQUE NOT NULL,
          username TEXT,
          first_name TEXT,
          last_name TEXT,
          language TEXT DEFAULT 'zh',
          settings TEXT DEFAULT '{}',
          created_at INTEGER DEFAULT (strftime('%s', 'now') * 1000),
          last_active INTEGER DEFAULT (strftime('%s', 'now') * 1000)
        )
      `);

      await database.run(`
        CREATE TABLE IF NOT EXISTS bot_subscriptions (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user_id INTEGER,
          type TEXT NOT NULL,
          config TEXT DEFAULT '{}',
          is_active BOOLEAN DEFAULT 1,
          created_at INTEGER DEFAULT (strftime('%s', 'now') * 1000),
          FOREIGN KEY (user_id) REFERENCES bot_users (id)
        )
      `);

      await database.run(`
        CREATE TABLE IF NOT EXISTS bot_usage_logs (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user_id INTEGER,
          command TEXT,
          parameters TEXT,
          response_time INTEGER,
          created_at INTEGER DEFAULT (strftime('%s', 'now') * 1000),
          FOREIGN KEY (user_id) REFERENCES bot_users (id)
        )
      `);

      logger.info('Bot database tables initialized');
    } catch (error) {
      logger.error('Failed to initialize bot tables:', error);
      throw error;
    }
  }

  async stop() {
    if (this.bot && this.isRunning) {
      await this.bot.stopPolling();
      this.isRunning = false;
      logger.info('Telegram bot stopped');
    }
  }

  // 獲取用戶信息
  async getUser(telegramId) {
    return await database.get(
      'SELECT * FROM bot_users WHERE telegram_id = ?',
      [telegramId.toString()]
    );
  }

  // 創建或更新用戶
  async upsertUser(userInfo) {
    const { id, username, first_name, last_name } = userInfo;
    
    await database.run(`
      INSERT OR REPLACE INTO bot_users 
      (telegram_id, username, first_name, last_name, last_active)
      VALUES (?, ?, ?, ?, ?)
    `, [
      id.toString(),
      username || null,
      first_name || null,
      last_name || null,
      Date.now()
    ]);

    return await this.getUser(id);
  }
}

export default HyperliquidBot;
