import dotenv from 'dotenv';
import TelegramBot from 'node-telegram-bot-api';

dotenv.config();

async function testSimpleBot() {
  console.log('🤖 Testing simple Telegram Bot...');

  try {
    // 檢查是否設置了 Bot Token
    if (!process.env.TELEGRAM_BOT_TOKEN) {
      console.warn('❌ TELEGRAM_BOT_TOKEN not set. Please set it in .env file to test the bot.');
      console.log('📝 Example: TELEGRAM_BOT_TOKEN=1234567890:ABCdefGHIjklMNOpqrsTUVwxyz');
      return;
    }

    console.log('✅ Bot token found');

    // 創建簡單的 Bot
    const bot = new TelegramBot(process.env.TELEGRAM_BOT_TOKEN, { polling: true });

    console.log('🎉 Telegram Bot created successfully!');

    // 測試 Bot 信息
    try {
      const me = await bot.getMe();
      console.log('🤖 Bot info:', {
        id: me.id,
        username: me.username,
        first_name: me.first_name
      });
    } catch (error) {
      console.error('❌ Failed to get bot info:', error.message);
      return;
    }

    // 設置基本指令處理
    bot.onText(/\/start/, (msg) => {
      const chatId = msg.chat.id;
      console.log('📨 Received /start from:', msg.from.username || msg.from.first_name);
      
      bot.sendMessage(chatId, 
        '🎉 歡迎使用 Hyperliquid 交易員追蹤機器人！\n\n' +
        '這是一個測試消息，證明機器人正常工作。\n\n' +
        '可用指令:\n' +
        '• /start - 顯示歡迎消息\n' +
        '• /help - 顯示幫助信息\n' +
        '• /test - 測試機器人響應'
      );
    });

    bot.onText(/\/help/, (msg) => {
      const chatId = msg.chat.id;
      console.log('📨 Received /help from:', msg.from.username || msg.from.first_name);
      
      bot.sendMessage(chatId, 
        '📚 Hyperliquid 交易員追蹤機器人幫助\n\n' +
        '🔧 這是測試版本，基本功能:\n' +
        '• /start - 開始使用\n' +
        '• /help - 顯示此幫助\n' +
        '• /test - 測試響應\n\n' +
        '💡 完整功能正在開發中...'
      );
    });

    bot.onText(/\/test/, (msg) => {
      const chatId = msg.chat.id;
      console.log('📨 Received /test from:', msg.from.username || msg.from.first_name);
      
      bot.sendMessage(chatId, 
        '✅ 機器人測試成功！\n\n' +
        `📊 當前時間: ${new Date().toLocaleString()}\n` +
        `👤 用戶: ${msg.from.first_name}\n` +
        `💬 聊天ID: ${chatId}`
      );
    });

    // 處理所有其他消息
    bot.on('message', (msg) => {
      if (!msg.text || msg.text.startsWith('/')) return;
      
      const chatId = msg.chat.id;
      console.log('📨 Received message from:', msg.from.username || msg.from.first_name, ':', msg.text);
      
      bot.sendMessage(chatId, 
        '🤖 我收到了您的消息！\n\n' +
        `您說: "${msg.text}"\n\n` +
        '請使用 /help 查看可用指令。'
      );
    });

    console.log('🚀 Bot is now running and ready to receive messages.');
    console.log('💬 Send /start to your bot to test it.');
    console.log('🛑 Press Ctrl+C to stop the bot');

    // 優雅關閉
    process.on('SIGINT', () => {
      console.log('\n🛑 Shutting down bot...');
      bot.stopPolling();
      console.log('✅ Bot stopped successfully');
      process.exit(0);
    });

    // 錯誤處理
    bot.on('error', (error) => {
      console.error('❌ Bot error:', error.message);
    });

    bot.on('polling_error', (error) => {
      console.error('❌ Polling error:', error.message);
    });

  } catch (error) {
    console.error('❌ Bot test failed:', error.message);
    console.error('Stack:', error.stack);
    process.exit(1);
  }
}

console.log('🚀 Starting bot test...');
testSimpleBot().catch(error => {
  console.error('❌ Unhandled error:', error.message);
  console.error('Stack:', error.stack);
  process.exit(1);
});
