import database from './src/models/database.js';

async function checkTables() {
  try {
    await database.initialize();
    
    const tables = await database.all("SELECT name FROM sqlite_master WHERE type='table'");
    console.log('Tables:', tables.map(t => t.name));
    
    // 檢查 bot_users 表
    if (tables.some(t => t.name === 'bot_users')) {
      const botUsersSchema = await database.all("PRAGMA table_info(bot_users)");
      console.log('\nbot_users schema:', botUsersSchema);
      
      const userCount = await database.get("SELECT COUNT(*) as count FROM bot_users");
      console.log('bot_users count:', userCount.count);
    }
    
    // 檢查 bot_subscriptions 表
    if (tables.some(t => t.name === 'bot_subscriptions')) {
      const subscriptionsSchema = await database.all("PRAGMA table_info(bot_subscriptions)");
      console.log('\nbot_subscriptions schema:', subscriptionsSchema);
    }
    
    await database.close();
  } catch (error) {
    console.error('Error:', error);
  }
}

checkTables();
