import { messageTemplates, botConfig } from '../config/index.js';
import logger from '../../utils/logger.js';
import database from '../../models/database.js';

class BasicCommands {
  constructor(bot) {
    this.bot = bot;
  }

  async start(msg, args) {
    const chatId = msg.chat.id;
    const user = msg.from;

    try {
      // 創建或更新用戶記錄
      await this.bot.upsertUser(user);

      // 獲取用戶語言設置
      const userRecord = await this.bot.getUser(user.id);
      const language = userRecord?.language || 'zh';

      // 發送歡迎消息
      const welcomeMessage = messageTemplates.welcome[language];
      
      // 創建內聯鍵盤
      const keyboard = {
        inline_keyboard: [
          [
            { text: '📊 查看頂級交易員', callback_data: 'top_traders:24h:1' },
            { text: '📈 查看統計數據', callback_data: 'stats:24h' }
          ],
          [
            { text: '🔍 搜索交易員', switch_inline_query_current_chat: '/search ' },
            { text: '🔔 通知設置', callback_data: 'alerts:manage' }
          ],
          [
            { text: '❓ 幫助', callback_data: 'help:main' },
            { text: '⚙️ 設置', callback_data: 'settings:main' }
          ]
        ]
      };

      await this.bot.bot.sendMessage(chatId, welcomeMessage, {
        reply_markup: keyboard,
        parse_mode: 'HTML'
      });

      logger.info(`User ${user.id} started the bot`);

    } catch (error) {
      logger.error('Error in start command:', error);
      await this.bot.sendErrorMessage(chatId, '初始化失敗，請稍後重試。');
    }
  }

  async help(msg, args) {
    const chatId = msg.chat.id;
    const user = msg.from;

    try {
      // 獲取用戶語言設置
      const userRecord = await this.bot.getUser(user.id);
      const language = userRecord?.language || 'zh';

      let helpMessage;

      if (args.length > 0) {
        // 顯示特定指令的幫助
        const command = args[0];
        helpMessage = await this.getCommandHelp(command, language);
      } else {
        // 顯示所有指令的幫助
        helpMessage = messageTemplates.help[language];
      }

      // 創建指令快捷鍵盤
      const keyboard = {
        inline_keyboard: [
          [
            { text: '👥 交易員查詢', callback_data: 'help:traders' },
            { text: '📊 統計查詢', callback_data: 'help:stats' }
          ],
          [
            { text: '🔔 通知管理', callback_data: 'help:alerts' },
            { text: '⚙️ 設置', callback_data: 'help:settings' }
          ],
          [
            { text: '🏠 返回主頁', callback_data: 'start:main' }
          ]
        ]
      };

      await this.bot.bot.sendMessage(chatId, helpMessage, {
        reply_markup: keyboard,
        parse_mode: 'HTML'
      });

    } catch (error) {
      logger.error('Error in help command:', error);
      await this.bot.sendErrorMessage(chatId, '獲取幫助信息失敗。');
    }
  }

  async status(msg, args) {
    const chatId = msg.chat.id;

    try {
      // 獲取系統狀態信息
      const status = await this.getSystemStatus();

      const statusMessage = `🔧 系統狀態報告

📊 數據庫狀態: ${status.database ? '✅ 正常' : '❌ 異常'}
🌐 API 連接: ${status.api ? '✅ 正常' : '❌ 異常'}
📡 WebSocket: ${status.websocket ? '✅ 已連接' : '❌ 斷開'}

👥 追蹤交易員: ${status.trackedTraders.toLocaleString()}
📈 今日新增: ${status.newTradersToday.toLocaleString()}
💰 24h 交易量: $${status.volume24h.toLocaleString()}

⏰ 最後更新: ${new Date(status.lastUpdate).toLocaleString('zh-CN')}
🕐 運行時間: ${status.uptime}

📊 機器人統計:
• 活躍用戶: ${status.activeUsers}
• 今日指令: ${status.commandsToday}
• 訂閱用戶: ${status.subscribedUsers}`;

      // 創建刷新按鈕
      const keyboard = {
        inline_keyboard: [
          [
            { text: '🔄 刷新狀態', callback_data: 'status:refresh' },
            { text: '📊 詳細統計', callback_data: 'status:detailed' }
          ],
          [
            { text: '🏠 返回主頁', callback_data: 'start:main' }
          ]
        ]
      };

      await this.bot.bot.sendMessage(chatId, statusMessage, {
        reply_markup: keyboard,
        parse_mode: 'HTML'
      });

    } catch (error) {
      logger.error('Error in status command:', error);
      await this.bot.sendErrorMessage(chatId, '獲取系統狀態失敗。');
    }
  }

  async getCommandHelp(command, language = 'zh') {
    const helpTexts = {
      zh: {
        top: `📊 /top - 頂級交易員排行榜

用法：
/top - 顯示前10名交易員（24小時）
/top 20 - 顯示前20名交易員
/top 10 1h - 顯示前10名交易員（1小時）

支持的時間範圍：
• 1h - 1小時
• 4h - 4小時  
• 24h - 24小時
• 7d - 7天
• 30d - 30天`,

        trader: `🔍 /trader - 查詢特定交易員

用法：
/trader 0x1234...5678 - 查詢指定地址的交易員信息

顯示信息包括：
• 基本統計（勝率、收益率等）
• 最近交易記錄
• 持倉情況
• 歷史績效`,

        stats: `📈 /stats - 交易統計

用法：
/stats - 顯示24小時統計
/stats 1h - 顯示1小時統計
/stats 7d - 顯示7天統計

統計內容包括：
• 活躍交易員數量
• 總交易量
• 平均收益率
• 熱門幣種分析`
      },
      en: {
        top: `📊 /top - Top Trader Leaderboard

Usage:
/top - Show top 10 traders (24h)
/top 20 - Show top 20 traders
/top 10 1h - Show top 10 traders (1h)

Supported timeframes:
• 1h - 1 hour
• 4h - 4 hours
• 24h - 24 hours
• 7d - 7 days
• 30d - 30 days`,

        trader: `🔍 /trader - Query Specific Trader

Usage:
/trader 0x1234...5678 - Query trader info by address

Information includes:
• Basic statistics (win rate, profitability, etc.)
• Recent trading records
• Position details
• Historical performance`,

        stats: `📈 /stats - Trading Statistics

Usage:
/stats - Show 24h statistics
/stats 1h - Show 1h statistics
/stats 7d - Show 7d statistics

Statistics include:
• Active trader count
• Total trading volume
• Average profitability
• Popular coin analysis`
      }
    };

    return helpTexts[language]?.[command] || messageTemplates.help[language];
  }

  async getSystemStatus() {
    try {
      // 獲取數據庫狀態
      const dbStatus = await this.checkDatabaseStatus();
      
      // 獲取交易員統計
      const traderStats = await this.getTraderStats();
      
      // 獲取機器人統計
      const botStats = await this.getBotStats();

      // 計算運行時間
      const uptime = this.formatUptime(process.uptime());

      return {
        database: dbStatus,
        api: true, // 簡化處理，實際應該檢查 API 狀態
        websocket: true, // 簡化處理，實際應該檢查 WebSocket 狀態
        trackedTraders: traderStats.total || 0,
        newTradersToday: traderStats.newToday || 0,
        volume24h: traderStats.volume24h || 0,
        lastUpdate: Date.now(),
        uptime: uptime,
        activeUsers: botStats.activeUsers || 0,
        commandsToday: botStats.commandsToday || 0,
        subscribedUsers: botStats.subscribedUsers || 0
      };
    } catch (error) {
      logger.error('Error getting system status:', error);
      throw error;
    }
  }

  async checkDatabaseStatus() {
    try {
      await database.get('SELECT 1');
      return true;
    } catch (error) {
      return false;
    }
  }

  async getTraderStats() {
    try {
      const total = await database.get('SELECT COUNT(*) as count FROM traders');
      
      const today = Date.now() - 24 * 60 * 60 * 1000;
      const newToday = await database.get(
        'SELECT COUNT(*) as count FROM traders WHERE created_at > ?',
        [today]
      );

      // 簡化的交易量計算
      const volume = await database.get(`
        SELECT SUM(size * price) as volume 
        FROM trades 
        WHERE timestamp > ?
      `, [today]);

      return {
        total: total?.count || 0,
        newToday: newToday?.count || 0,
        volume24h: volume?.volume || 0
      };
    } catch (error) {
      logger.error('Error getting trader stats:', error);
      return {};
    }
  }

  async getBotStats() {
    try {
      const today = Date.now() - 24 * 60 * 60 * 1000;
      
      const activeUsers = await database.get(
        'SELECT COUNT(*) as count FROM bot_users WHERE last_active > ?',
        [today]
      );

      const commandsToday = await database.get(
        'SELECT COUNT(*) as count FROM bot_usage_logs WHERE created_at > ?',
        [today]
      );

      const subscribedUsers = await database.get(
        'SELECT COUNT(DISTINCT user_id) as count FROM bot_subscriptions WHERE is_active = 1'
      );

      return {
        activeUsers: activeUsers?.count || 0,
        commandsToday: commandsToday?.count || 0,
        subscribedUsers: subscribedUsers?.count || 0
      };
    } catch (error) {
      logger.error('Error getting bot stats:', error);
      return {};
    }
  }

  formatUptime(seconds) {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (days > 0) {
      return `${days}天 ${hours}小時 ${minutes}分鐘`;
    } else if (hours > 0) {
      return `${hours}小時 ${minutes}分鐘`;
    } else {
      return `${minutes}分鐘`;
    }
  }
}

export default BasicCommands;
